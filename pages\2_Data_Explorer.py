import streamlit as st
import polars as pl
import pandas as pd  # Keep for compatibility with profiling tools
import sys
import os
import pygwalker as pyg
from pygwalker.api.streamlit import StreamlitRenderer
import time
from datetime import datetime
import logging
import traceback
import json
import numpy as np
import io
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import copy
import hashlib
try:
    import pyarrow as pa
    PYARROW_AVAILABLE = True
except ImportError:
    PYARROW_AVAILABLE = False
from app_core.utils.styling import apply_css
from app_core.config import (
    init_session_state, get_active_dataset, get_dataset_sample, 
    store_dataset, remove_dataset
)

st.set_page_config(
    layout='wide',
    page_title="Data Explorer - Visualize and analyze your data",
    page_icon="📊"
)

# Initialize session state
init_session_state()

# Apply CSS styling
apply_css()

# Set up logging for this page
logger = logging.getLogger(__name__)
logger.info("Data Explorer page loaded")

def create_dataframe_hash(df: pd.DataFrame) -> str:
    """
    Create a hash for a DataFrame to enable proper caching.
    
    Args:
        df: Pandas DataFrame
        
    Returns:
        str: Hash string for the DataFrame
    """
    try:
        # Create a hash based on shape, columns, and a sample of data
        df_info = f"{df.shape}_{list(df.columns)}_{df.dtypes.to_dict()}"
        if len(df) > 0:
            # Add a sample of data to the hash
            sample_data = df.head(min(100, len(df))).to_string()
            df_info += sample_data
        return hashlib.md5(df_info.encode()).hexdigest()
    except Exception as e:
        logger.warning(f"Could not create DataFrame hash: {e}. Using timestamp.")
        return hashlib.md5(str(time.time()).encode()).hexdigest()

# Cache PyGWalker renderer to avoid memory issues
@st.cache_resource
def get_pyg_renderer(dataset_name: str, df_hash: str, df_pandas: pd.DataFrame, spec_config: dict = None) -> StreamlitRenderer:
    """
    Create and cache a PyGWalker renderer for the given dataset.
    
    Args:
        dataset_name: Name of the dataset for caching purposes
        df_hash: Hash of the DataFrame for cache invalidation
        df_pandas: Pandas DataFrame (converted from Polars for PyGWalker compatibility)
        spec_config: Optional visualization configuration
    
    Returns:
        StreamlitRenderer: Cached PyGWalker renderer
    """
    logger.info(f"Creating PyGWalker renderer for dataset: {dataset_name} (hash: {df_hash[:8]}...) with {df_pandas.shape[0]:,} rows")
      # Use the full dataset without sampling
    df_for_pygwalker = df_pandas

    try:
        if spec_config:
            return StreamlitRenderer(
                df_for_pygwalker, 
                spec=spec_config,
                spec_io_mode="rw",
                appearance="light",
                kernel_computation=True,
                height=800,
            )
        else:
            return StreamlitRenderer(
                df_for_pygwalker,
                spec_io_mode="rw", 
                appearance="light",
                kernel_computation=True,
                height=800,
            )
    except Exception as e:
        logger.error(f"Failed to create PyGWalker renderer: {str(e)}")
        raise
    

def validate_data_for_pygwalker(df: pd.DataFrame) -> tuple[bool, str, pd.DataFrame]:
    """
    Validate and clean DataFrame for PyGWalker/DuckDB compatibility.
    
    Args:
        df: Input DataFrame
        
    Returns:
        tuple: (is_valid, error_message, cleaned_dataframe)
    """
    try:
        cleaned_df = df.copy()
        issues_found = []
        
        # Check for problematic column names
        problematic_cols = []
        for col in cleaned_df.columns:
            if not isinstance(col, str):
                problematic_cols.append(col)
        
        if problematic_cols:
            issues_found.append(f"Non-string column names: {problematic_cols}")
            cleaned_df.columns = [str(col) for col in cleaned_df.columns]
        
        # Check for mixed data types that cause DuckDB issues
        for col in cleaned_df.columns:
            col_data = cleaned_df[col]
            
            # Skip if all values are null
            if col_data.isna().all():
                continue
                
            # Handle object columns with potential mixed types
            if col_data.dtype == 'object':
                # Check if it's actually numeric data stored as object
                try:
                    # Try to convert a sample to numeric
                    sample = col_data.dropna().head(1000)
                    if len(sample) > 0:
                        numeric_converted = pd.to_numeric(sample, errors='coerce')
                        if not numeric_converted.isna().all():
                            # Looks like numeric data - convert the whole column
                            cleaned_df[col] = pd.to_numeric(col_data, errors='coerce').fillna(0)
                            issues_found.append(f"Converted {col} from object to numeric")
                            continue
                except:
                    pass
                
                # Clean string data
                try:
                    # Convert to string and clean
                    string_data = col_data.astype(str)
                    # Remove problematic characters that might cause DuckDB issues
                    cleaned_df[col] = string_data.str.replace(r'[^\x00-\x7F]+', '', regex=True)  # Remove non-ASCII
                    cleaned_df[col] = cleaned_df[col].str.strip()  # Remove whitespace
                    
                    # If too many unique values, keep as string, otherwise convert to category
                    if cleaned_df[col].nunique() / len(cleaned_df) < 0.3:
                        cleaned_df[col] = cleaned_df[col].astype('category')
                        issues_found.append(f"Converted {col} to category")
                        
                except Exception as e:
                    issues_found.append(f"Could not clean column {col}: {str(e)}")
        
        # Ensure index is clean
        cleaned_df = cleaned_df.reset_index(drop=True)
          # Final validation - try to convert to Arrow (similar to what DuckDB does)
        if PYARROW_AVAILABLE:
            try:
                pa.Table.from_pandas(cleaned_df.head(100))  # Test with sample
            except Exception as e:
                return False, f"Arrow conversion failed: {str(e)}", cleaned_df
        else:
            # If PyArrow is not available, skip this validation
            logger.warning("PyArrow not available, skipping Arrow validation")
        
        message = f"Data validation passed. Issues fixed: {len(issues_found)}"
        if issues_found:
            message += f"\nFixed issues: {'; '.join(issues_found[:3])}"
            if len(issues_found) > 3:
                message += f" (and {len(issues_found) - 3} more)"
        
        return True, message, cleaned_df
        
    except Exception as e:
        return False, f"Data validation error: {str(e)}", df



def should_sample_dataset(df_pandas: pd.DataFrame, max_rows: int = 100000) -> bool:
    """
    Determine if dataset should be sampled for performance reasons.
    
    Args:
        df_pandas: Pandas DataFrame
        max_rows: Maximum rows before sampling is recommended
        
    Returns:
        bool: True if sampling is recommended
    """
    return df_pandas.shape[0] > max_rows

def sample_large_dataset(df_pandas: pd.DataFrame, sample_size: int = 50000, method: str = "random") -> pd.DataFrame:
    """
    Sample a large dataset for better performance in visualizations.
    
    Args:
        df_pandas: Pandas DataFrame
        sample_size: Number of rows to sample
        method: Sampling method ('random', 'first', 'last')
        
    Returns:
        pd.DataFrame: Sampled DataFrame
    """
    total_rows = df_pandas.shape[0]
    
    if total_rows <= sample_size:
        return df_pandas
    
    logger.info(f"Sampling dataset from {total_rows:,} to {sample_size:,} rows using {method} method")
    
    if method == "random":
        return df_pandas.sample(n=sample_size, random_state=42)
    elif method == "first":
        return df_pandas.head(sample_size)
    elif method == "last":
        return df_pandas.tail(sample_size)
    else:
        # Default to random sampling
        return df_pandas.sample(n=sample_size, random_state=42)

def ensure_pandas_compatibility(df_pandas: pd.DataFrame, max_rows_for_processing: int = 500000) -> pd.DataFrame:
    """
    Ensure Pandas DataFrame is optimized for processing and visualization tools.
    Handles large datasets by providing optimization warnings and fixes DuckDB compatibility issues.
    
    Args:
        df_pandas: Pandas DataFrame
        max_rows_for_processing: Maximum rows for efficient processing
        
    Returns:
        pd.DataFrame: Optimized Pandas DataFrame compatible with PyGWalker/DuckDB
    """
    try:
        # Check if dataset is large
        if df_pandas.shape[0] > max_rows_for_processing:
            logger.warning(f"Dataset has {df_pandas.shape[0]:,} rows. This may cause performance issues.")
            
        # Create a copy to avoid modifying the original
        optimized_df = df_pandas.copy()
        
        # Fix mixed-type columns that cause DuckDB casting errors
        for col in optimized_df.columns:
            col_dtype = optimized_df[col].dtype
            
            # Handle object columns that might have mixed types
            if col_dtype == 'object':
                try:
                    # Try to convert to numeric if possible
                    numeric_col = pd.to_numeric(optimized_df[col], errors='coerce')
                    if not numeric_col.isna().all():
                        # If conversion worked for some values, fill NaNs and use numeric
                        optimized_df[col] = numeric_col.fillna(0)
                        continue
                except Exception:
                    pass
                
                # Clean string columns - remove leading/trailing whitespace
                try:
                    # Convert to string and handle NaN values
                    string_col = optimized_df[col].astype(str).replace('nan', '')
                    optimized_df[col] = string_col.str.strip()
                    
                    # Convert to category if it has limited unique values (helps with memory and performance)
                    unique_ratio = optimized_df[col].nunique() / len(optimized_df)
                    if unique_ratio < 0.5 and optimized_df[col].nunique() < 1000:
                        optimized_df[col] = optimized_df[col].astype('category')
                except Exception:
                    # If all else fails, convert to string
                    optimized_df[col] = optimized_df[col].astype(str)
            
            # Handle numpy arrays and other problematic types
            elif str(col_dtype).startswith('object') or 'mixed' in str(col_dtype):
                try:
                    # Convert problematic columns to string
                    optimized_df[col] = optimized_df[col].astype(str)
                except Exception:
                    pass
        
        # Reset index to ensure clean integer index
        optimized_df = optimized_df.reset_index(drop=True)
        
        # Ensure all column names are strings (DuckDB requirement)
        optimized_df.columns = [str(col) for col in optimized_df.columns]
        
        # Remove any completely empty columns
        for col in optimized_df.columns:
            if optimized_df[col].isna().all() or (optimized_df[col].astype(str) == '').all():
                logger.warning(f"Removing empty column: {col}")
                optimized_df = optimized_df.drop(columns=[col])
        
        logger.info(f"DataFrame optimized: {optimized_df.shape[0]:,} rows, {optimized_df.shape[1]} columns")
        return optimized_df
        
    except Exception as e:
        logger.error(f"Error optimizing DataFrame: {str(e)}")
        logger.error(traceback.format_exc())
        # Return a basic cleaned version
        try:
            clean_df = df_pandas.copy()
            clean_df = clean_df.reset_index(drop=True)
            clean_df.columns = [str(col) for col in clean_df.columns]
            return clean_df
        except Exception:
            return df_pandas

def safe_load_dataset_for_visualization(lazy_dataset, use_sample: bool = False, sample_size: int = 50000) -> tuple[pd.DataFrame, bool]:
    """
    Safely load dataset for visualization with proper error handling and memory management.
    
    Args:
        lazy_dataset: LazyDataset object
        use_sample: Whether to use sampling (ignored - always loads full dataset)
        sample_size: Size of sample if sampling (ignored - always loads full dataset)
        
    Returns:
        tuple: (DataFrame, success_flag)
    """
    try:
        total_rows = lazy_dataset.shape[0]
        logger.info(f"Loading full dataset with {total_rows:,} rows")
        
        # Always load the full dataset
        df = lazy_dataset.get_full_data()
        
        # Check memory usage and warn user
        memory_mb = df.memory_usage(deep=True).sum() / 1024 / 1024
        if memory_mb > 1000:  # More than 1GB
            st.warning(f"⚠️ High memory usage: {memory_mb:.1f} MB")
        
        return df, True
        
    except MemoryError:
        logger.error("Memory error while loading dataset")
        st.error("❌ Memory error! Dataset too large for available memory.")
        return pd.DataFrame(), False
            
    except Exception as e:
        logger.error(f"Error loading dataset: {e}")
        st.error(f"❌ Error loading dataset: {str(e)}")
        return pd.DataFrame(), False

def get_pandas_column_info(df: pd.DataFrame) -> dict:
    """
    Get comprehensive information about DataFrame columns.
    
    Args:
        df: Pandas DataFrame
        
    Returns:
        dict: Column information including types and statistics
    """
    info = {
        "columns": df.columns.tolist(),
        "dtypes": {col: str(df[col].dtype) for col in df.columns},
        "shape": df.shape,
        "null_counts": {col: df[col].isnull().sum() for col in df.columns},
        "numeric_columns": df.select_dtypes(include=[np.number]).columns.tolist(),
        "string_columns": df.select_dtypes(include=['object', 'string']).columns.tolist(),
        "date_columns": df.select_dtypes(include=['datetime64']).columns.tolist()
    }
    return info

# Define a function to handle PyGWalker configuration
def handle_pygwalker_config():
    """
    Handle PyGWalker configuration in Streamlit.

    Note: The StreamlitRenderer doesn't directly expose the configuration,
    so we rely on the user to save configurations manually using the export
    functionality in PyGWalker and then saving it via the sidebar.
    """
    # Check if we have a configuration in session state
    if 'current_viz_config' not in st.session_state:
        st.session_state.current_viz_config = {}
        logger.info("Initialized empty PyGWalker configuration")
    
    # Force clear any existing config that might override the default tab setting
    # This ensures the "data" tab is always the default
    if st.session_state.current_viz_config:
        # Clear the config to use our hardcoded defaults
        st.session_state.current_viz_config = {}
        logger.info("Cleared existing PyGWalker configuration to enforce default tab setting")
    
    # Log the current state
    if st.session_state.current_viz_config:
        logger.info("PyGWalker configuration is available in session state")
    else:
        logger.info("No PyGWalker configuration in session state - will use defaults with data tab")

def calculate_optimal_parameters(df: pd.DataFrame) -> dict:
    """
    Calculate optimal performance parameters based on dataset dimensions.
    
    Args:
        df: Input DataFrame
        
    Returns:
        dict: Optimal parameters for the dataset
    """
    rows, cols = df.shape
    
    # Calculate memory usage in MB
    memory_mb = df.memory_usage(deep=True).sum() / 1024 / 1024
    
    # Base parameters for small datasets
    params = {
        'auto_sample_threshold': 100000,
        'memory_warning_mb': 200,
        'max_display_rows': 10000,
        'chunk_size': 5000,
        'enable_lazy_loading': False,
        'pygwalker_sample_size': 25000
    }
    
    # Adjust based on number of rows
    if rows <= 10000:
        # Small dataset - no sampling needed
        params.update({
            'auto_sample_threshold': rows + 1,  # Never sample
            'memory_warning_mb': 50,
            'max_display_rows': rows,
            'chunk_size': min(rows, 1000),
            'enable_lazy_loading': False,
            'pygwalker_sample_size': rows
        })
    elif rows <= 100000:
        # Medium dataset - light optimizations
        params.update({
            'auto_sample_threshold': 50000,
            'memory_warning_mb': 100,
            'max_display_rows': 50000,
            'chunk_size': 5000,
            'enable_lazy_loading': False,
            'pygwalker_sample_size': min(rows, 25000)
        })
    elif rows <= 500000:
        # Large dataset - moderate optimizations
        params.update({
            'auto_sample_threshold': 100000,
            'memory_warning_mb': 300,
            'max_display_rows': 100000,
            'chunk_size': 10000,
            'enable_lazy_loading': True,
            'pygwalker_sample_size': 50000
        })
    elif rows <= 2000000:
        # Very large dataset - aggressive optimizations
        params.update({
            'auto_sample_threshold': 200000,
            'memory_warning_mb': 500,
            'max_display_rows': 200000,
            'chunk_size': 20000,
            'enable_lazy_loading': True,
            'pygwalker_sample_size': 75000
        })
    else:
        # Massive dataset - maximum optimizations
        params.update({
            'auto_sample_threshold': 500000,
            'memory_warning_mb': 1000,
            'max_display_rows': 500000,
            'chunk_size': 50000,
            'enable_lazy_loading': True,
            'pygwalker_sample_size': 100000
        })
    
    # Adjust based on number of columns
    if cols > 100:
        # Many columns - reduce sample sizes and increase memory warnings
        params['memory_warning_mb'] = max(params['memory_warning_mb'], 300)
        params['pygwalker_sample_size'] = min(params['pygwalker_sample_size'], 50000)
        params['chunk_size'] = min(params['chunk_size'], 5000)
    elif cols > 50:
        # Moderate number of columns
        params['memory_warning_mb'] = max(params['memory_warning_mb'], 200)
        params['pygwalker_sample_size'] = min(params['pygwalker_sample_size'], 75000)
    
    # Adjust based on actual memory usage
    if memory_mb > 1000:
        # High memory usage - be more conservative
        params['memory_warning_mb'] = max(params['memory_warning_mb'], 800)
        params['auto_sample_threshold'] = min(params['auto_sample_threshold'], 100000)
        params['pygwalker_sample_size'] = min(params['pygwalker_sample_size'], 50000)
        params['enable_lazy_loading'] = True
    elif memory_mb > 500:
        # Moderate memory usage
        params['memory_warning_mb'] = max(params['memory_warning_mb'], 400)
        params['auto_sample_threshold'] = min(params['auto_sample_threshold'], 200000)
        params['enable_lazy_loading'] = True
    
    # Add metadata about the decision
    params['dataset_info'] = {
        'rows': rows,
        'cols': cols,
        'memory_mb': round(memory_mb, 2),
        'size_category': (
            'small' if rows <= 10000 else
            'medium' if rows <= 100000 else
            'large' if rows <= 500000 else
            'very_large' if rows <= 2000000 else
            'massive'
        )
    }
    
    return params

# Initialize Great Expectations related session state variables if not already initialized
if 'expectation_suites' not in st.session_state:
    st.session_state.expectation_suites = {}

if 'validation_results' not in st.session_state:
    st.session_state.validation_results = {}

# Apply centralized CSS
apply_css()

# Add custom button styling for readability
st.markdown("""
<style>
div[data-testid="stButton"] button {
    color: white !important;
    font-weight: 500 !important;
}
</style>
""", unsafe_allow_html=True)

# Add custom CSS to hide the "app" text in the sidebar and improve the UI
st.markdown("""
<style>
/* Hide the "app" text in the sidebar */
section[data-testid="stSidebar"] div.element-container:first-child {
    display: none;
}

/* Make the PyGWalker component take up more space */
iframe {
    height: 1200px !important;
    width: 100% !important;
    border: none !important;
}

/* Style the tabs */
.stTabs [data-baseweb="tab-list"] {
    gap: 24px;
}

.stTabs [data-baseweb="tab"] {
    height: 50px;
    white-space: pre-wrap;
    border-radius: 4px 4px 0 0;
    padding: 10px 16px;
    font-weight: 500;
}

/* Style the active tab */
.stTabs [aria-selected="true"] {
    background-color: #f0f2f6;
    border-bottom: 2px solid #4e8cff;
}

/* Improve scrolling for pandas profiling report */
.pandas-profiling-report {
    overflow-y: auto !important;
    max-height: 100% !important;
}

/* Make sure the iframe for the report takes up enough space */
iframe[srcdoc*="pandas-profiling"], iframe[srcdoc*="ydata-profiling"] {
    height: 3000px !important;
    width: 100% !important;
    border: none !important;
    overflow-y: scroll !important;
}

/* Improve navigation bar visibility in the profiling report */
iframe[srcdoc*="pandas-profiling"] .navbar,
iframe[srcdoc*="ydata-profiling"] .navbar,
iframe[srcdoc*="pandas-profiling"] .nav,
iframe[srcdoc*="ydata-profiling"] .nav {
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
}

/* Make sure the content is fully visible */
iframe[srcdoc*="pandas-profiling"] body,
iframe[srcdoc*="ydata-profiling"] body {
    overflow: visible !important;
    height: auto !important;
}

/* Hide YData branding in the iframe */
iframe[srcdoc*="pandas-profiling"] .ydata-credit,
iframe[srcdoc*="pandas-profiling"] div[class*="ydata-credit"],
iframe[srcdoc*="pandas-profiling"] div[class*="credit-ydata"],
iframe[srcdoc*="pandas-profiling"] .credit-ydata,
iframe[srcdoc*="pandas-profiling"] .footer-brand,
iframe[srcdoc*="pandas-profiling"] footer div.credit,
iframe[srcdoc*="pandas-profiling"] div[class*="credit"],
iframe[srcdoc*="ydata-profiling"] .ydata-credit,
iframe[srcdoc*="ydata-profiling"] div[class*="ydata-credit"],
iframe[srcdoc*="ydata-profiling"] div[class*="credit-ydata"],
iframe[srcdoc*="ydata-profiling"] .credit-ydata,
iframe[srcdoc*="ydata-profiling"] .footer-brand,
iframe[srcdoc*="ydata-profiling"] footer div.credit,
iframe[srcdoc*="ydata-profiling"] div[class*="credit"] {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    opacity: 0 !important;
}
</style>
""", unsafe_allow_html=True)

# Hide the Streamlit deprecation warning alert for st.experimental_user
st.markdown("""
<style>
  /* hide any warning-alert container */
  div[role="alert"][data-baseweb="notification"] {
    display: none !important;
  }
</style>
""", unsafe_allow_html=True)

# Import ydata_profiling and streamlit component after page config
try:
    import polars as pl
    # Make sure matplotlib is available
    import matplotlib.pyplot as plt

    # Use ydata_profiling for profiling reports
    from ydata_profiling import ProfileReport
    PANDAS_PROFILING_AVAILABLE = True
except ImportError:
    PANDAS_PROFILING_AVAILABLE = False
    st.warning("Pandas Profiling is not available. Please install it with: pip install ydata-profiling matplotlib")

# Session state variables for PyGWalker are now initialized in config.py
# Initialize and check PyGWalker configuration
handle_pygwalker_config()
logger.info("Using PyGWalker session state variables")

# Create a sidebar with options
with st.sidebar:
    st.header("Explorer Options")

    # Add dataset selector if multiple datasets exist
    if len(st.session_state.lazy_datasets) > 0:
        st.subheader("Select Dataset")

        # Create a radio button for dataset selection
        dataset_options = list(st.session_state.lazy_datasets.keys())
        selected_dataset = st.radio(
            "Choose a dataset to explore:",
            dataset_options,
            index=dataset_options.index(st.session_state.active_dataset) if st.session_state.active_dataset in dataset_options else 0
        )

        # Update the active dataset if changed
        if selected_dataset != st.session_state.active_dataset:
            st.session_state.active_dataset = selected_dataset
            st.rerun()
        
        # Show dataset info
        if st.session_state.active_dataset:
            lazy_dataset = st.session_state.lazy_datasets[st.session_state.active_dataset]
            st.info(f"""
            **Dataset Info:**
            - Rows: {lazy_dataset.shape[0]:,}
            - Columns: {lazy_dataset.shape[1]}
            - Memory: {lazy_dataset.memory_usage_mb:.1f} MB
            """)

# Main content - check if we have data
if not st.session_state.has_data or not st.session_state.lazy_datasets:
    # Enhanced empty state presentation

    
    # Create centered layout with columns
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # Main heading with icon
        st.markdown("""
        <div style="text-align: center;">
            <h1 style="color: #1f77b4;">
                📊 Data Explorer
            </h1>
            <p style="font-size: 1.2rem; color: #666; margin-bottom: 2rem;">
                Discover insights in your data with interactive visualizations
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # Info box about what's available
        st.info("""
        🔍 **What you can do with Data Explorer:**
        
        • **Visual Explorer**: Interactive charts and graphs with PyGWalker
        • **Data Profiling**: Comprehensive statistical analysis 
        • **Data Quality**: Identify missing values, duplicates, and anomalies
        • **Dataset Comparison**: Switch between multiple uploaded datasets
        """)
        
        # Getting started section
        st.markdown("""
        ### 🚀 Get Started
        
        To start exploring your data, you'll need to upload at least one dataset first.
        """)
        
        # Action buttons
        st.markdown("<br>", unsafe_allow_html=True)
        
        # Create two columns for buttons
        btn_col1, btn_col2 = st.columns(2)
        
        with btn_col1:
            if st.button("📤 Upload Your Data", type="primary", use_container_width=True):
                st.switch_page("pages/1_Upload_Data.py")
        
        with btn_col2:
            if st.button("📥 Import from Oracle", type="primary", use_container_width=True):
                st.switch_page("pages/3_Database_Connections.py")
      # Add some spacing
    st.markdown("<br><br>", unsafe_allow_html=True)
    
else:
    # Add page header when datasets are available
    st.header("📊 Data Explorer")
    
    # Create tabs for different views
    tab1, tab2, tab3 = st.tabs(["📊 Visual Explorer", "📝 Data Profiling", "✅ Data Quality"])
    
    with tab1:
        # Get the active lazy dataset
        lazy_dataset = get_active_dataset()
        
        if lazy_dataset:
              # Load full dataset for visualization
            st.info("Loading full dataset for visualization...")
            with st.spinner("Loading full dataset..."):
                # Use the safe loading function for full dataset
                df_for_viz, success = safe_load_dataset_for_visualization(lazy_dataset, use_sample=False)# Check if loading was successful
            if not success or df_for_viz.empty:
                st.error("❌ Failed to load dataset. Please try with a smaller sample size.")
            else:
                # Optimize and validate the DataFrame for visualization
                with st.spinner("Optimizing and validating data for visualization..."):
                    df_for_viz_optimized = ensure_pandas_compatibility(df_for_viz)
                    
                    # Validate data for PyGWalker/DuckDB compatibility
                    is_valid, validation_msg, df_validated = validate_data_for_pygwalker(df_for_viz_optimized)
                    
                    if not is_valid:
                        st.error(f"❌ Data validation failed: {validation_msg}")
                        st.info("💡 Showing fallback data preview instead of PyGWalker visualization.")
                        df_for_viz_final = df_for_viz_optimized
                    else:
                        if "Issues fixed:" in validation_msg:
                            st.info(f"✅ {validation_msg}")
                        df_for_viz_final = df_validated
                  # PyGWalker visualization
                if is_valid:
                    try:
                        logger.info(f"Rendering PyGWalker for dataset '{st.session_state.active_dataset}'")
                        
                        # Create hash for caching
                        df_hash = create_dataframe_hash(df_for_viz_final)
                          # Use cached renderer
                        pyg_renderer = get_pyg_renderer(
                            dataset_name=st.session_state.active_dataset,
                            df_hash=df_hash,
                            df_pandas=df_for_viz_final,
                            spec_config=st.session_state.current_viz_config if st.session_state.current_viz_config else None
                        )

                        # Display the explorer with data tab as default
                        pyg_renderer.explorer(default_tab="data")

                        if not st.session_state.current_viz_config:
                            st.info("PyGWalker started with default configuration.")
                        else:
                            st.success("PyGWalker loaded with saved configuration.")
                        
                        logger.info("PyGWalker visualization rendered successfully.")

                    except Exception as e:
                        error_msg = f"Error rendering PyGWalker: {str(e)}"
                        logger.error(error_msg)
                        logger.error(traceback.format_exc())
                        st.error(f"Could not render PyGWalker: {str(e)}. Showing data preview instead.")
                        is_valid = False
                
                # Fallback data preview
                if not is_valid:
                    st.subheader("Data Preview (Fallback)")
                    
                    # Show column information
                    col_info = lazy_dataset.get_column_info()
                    
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("Numeric Cols", len(col_info['numeric_columns']))
                    with col2:
                        st.metric("String Cols", len(col_info['string_columns']))
                    with col3:
                        st.metric("Date Cols", len(col_info['date_columns']))
                    with col4:
                        total_nulls = sum(col_info['null_counts'].values())
                        st.metric("Null Values", f"{total_nulls:,}")
                    
                    # Show data types
                    st.subheader("Column Information")
                    dtype_df = pd.DataFrame({
                        "Column": list(col_info['dtypes'].keys()),
                        "Data Type": list(col_info['dtypes'].values()),
                        "Null Count": [col_info['null_counts'][col] for col in col_info['dtypes'].keys()]
                    })
                    st.dataframe(dtype_df, use_container_width=True)
                    
                    # Show data preview using cached sample
                    st.subheader("Data Sample")
                    preview_df = lazy_dataset.get_sample_cached(100)
                    st.dataframe(preview_df, use_container_width=True)

    with tab2:        
        if PANDAS_PROFILING_AVAILABLE:
            # Get lazy dataset
            lazy_dataset = get_active_dataset()
            
            if lazy_dataset:
                st.info(f"Dataset: {lazy_dataset.shape[0]:,} rows × {lazy_dataset.shape[1]} columns")
                
                # Profiling options
                report_mode = st.radio(
                    "Select Profiling Mode:",
                    options=["Normal (Minimal)", "Detailed (Explorative)"],
                    index=0
                )
                if st.button("Generate Profiling Report"):
                    with st.spinner("Generating profiling report..."):
                        try:
                            # Get full dataset for profiling
                            st.info(f"Using full dataset ({lazy_dataset.shape[0]:,} rows) for profiling")
                            df_for_profiling = lazy_dataset.get_full_data()
                            
                            # Ensure compatibility
                            df_for_profiling = ensure_pandas_compatibility(df_for_profiling)
                            
                            # Generate profile
                            profile = ProfileReport(
                                df_for_profiling,
                                title=f"Profiling Report - {st.session_state.active_dataset}",
                                minimal=(report_mode == "Normal (Minimal)"),
                                explorative=(report_mode == "Detailed (Explorative)")
                            )
                            
                            st.markdown("### Profiling Report")
                            st.components.v1.html(profile.to_html(), height=1000, scrolling=True)
                            
                            st.success("✅ Profiling report generated successfully!")
                            
                        except Exception as e:
                            st.error(f"Error generating profiling report: {str(e)}")
                            logger.error(f"Profiling error: {str(e)}")
                            
                            # Show fallback summary using lazy dataset metadata
                            st.markdown("### Fallback Data Summary")
                            col_info = lazy_dataset.get_column_info()
                            
                            col1, col2, col3, col4 = st.columns(4)
                            with col1:
                                st.metric("Rows", f"{lazy_dataset.shape[0]:,}")
                            with col2:
                                st.metric("Columns", lazy_dataset.shape[1])
                            with col3:
                                st.metric("Numeric Cols", len(col_info['numeric_columns']))
                            with col4:
                                total_nulls = sum(col_info['null_counts'].values())
                                st.metric("Null Values", f"{total_nulls:,}")
        else:
            st.error("YData Profiling not available. Please install: pip install ydata-profiling matplotlib")

    with tab3:
        # Data Quality tab implementation using lazy dataset
        lazy_dataset = get_active_dataset()
        
        if lazy_dataset:
              # Load data for quality checks
            @st.cache_data
            def load_quality_data(dataset_name: str) -> pd.DataFrame:
                """Load data for quality checks with caching"""
                lazy_ds = st.session_state.lazy_datasets[dataset_name]
                # Always use full dataset for quality checks
                return lazy_ds.get_full_data()
            
            # Load data for quality checks
            try:
                quality_df = load_quality_data(st.session_state.active_dataset)
            except Exception as e:
                st.error(f"Error loading data for quality checks: {str(e)}")
                st.stop()
            
            # Show dataset info
            st.subheader("Dataset Information")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Rows", f"{lazy_dataset.shape[0]:,}")
            with col2:
                st.metric("Columns", lazy_dataset.shape[1])
            with col3:
                total_nulls = quality_df.isnull().sum().sum()
                st.metric("Missing Values", f"{total_nulls:,}")
            
            # Create subtabs for different data quality operations
            quality_tab1, quality_tab2, quality_tab3 = st.tabs(["📋 Define Expectations", "🔍 Run Validations", "📊 Quality Reports"])
            
            with quality_tab1:
                st.subheader("Define Data Quality Expectations")

                # Create a new expectation suite
                st.subheader("Create Expectation Suite")

                suite_name = st.text_input("Expectation Suite Name", f"{st.session_state.active_dataset}_quality_suite")

                # Column selection for expectations
                st.subheader("Select Columns for Validation")
                # Display columns with checkboxes
                columns = quality_df.columns.tolist()
                selected_columns = st.multiselect("Select columns to validate", columns, default=columns[:5])

                if selected_columns:
                    st.subheader("Define Expectations")

                    # Create expandable sections for different types of expectations
                    with st.expander("Column Existence and Type Expectations", expanded=True):
                        st.markdown("These expectations validate the structure of your data.")

                        # Expect columns to exist
                        st.checkbox("Expect these columns to exist", value=True, key="expect_columns_to_exist")

                        # Expect column types
                        st.checkbox("Expect column types to match schema", value=True, key="expect_column_types")

                        if st.session_state.expect_column_types:
                            st.info("Column types will be inferred from the current data.")

                    with st.expander("Missing Value Expectations", expanded=True):
                        st.markdown("These expectations validate the completeness of your data.")
                        # For each selected column, add missing value expectations
                        for column in selected_columns:
                            has_nulls = quality_df[column].isnull().sum() > 0
                            st.checkbox(f"Expect '{column}' values to not be null",
                                       value=not has_nulls,
                                       key=f"not_null_{column}")

                    with st.expander("Uniqueness Expectations", expanded=True):
                        st.markdown("These expectations validate the uniqueness of values in your data.")
                        # For each selected column, add uniqueness expectations
                        for column in selected_columns:
                            unique_count = quality_df[column].nunique()
                            total_count = quality_df.shape[0] - quality_df[column].isnull().sum()
                            is_unique = unique_count == total_count
                            st.checkbox(f"Expect '{column}' values to be unique",
                                       value=is_unique,
                                       key=f"unique_{column}")

                    with st.expander("Value Range Expectations", expanded=True):
                        st.markdown("These expectations validate the range of values in your data.")

                        # For each selected numeric column, add range expectations
                        numeric_columns = [col for col in selected_columns if quality_df[col].dtype.kind in 'biufc']

                        if numeric_columns:
                            for column in numeric_columns:
                                min_val = float(quality_df[column].min())
                                max_val = float(quality_df[column].max())

                                st.subheader(f"Range for '{column}'")
                                use_range = st.checkbox(f"Expect '{column}' values to be in range",
                                                      value=True,
                                                      key=f"range_{column}")

                                if use_range:
                                    min_range, max_range = st.slider(
                                        f"Expected range for '{column}'",
                                        float(min_val - abs(min_val*0.1)),
                                        float(max_val + abs(max_val*0.1)),
                                        (float(min_val), float(max_val)),
                                        key=f"range_slider_{column}"
                                    )
                                    st.write(f"Min: {min_range}, Max: {max_range}")
                        else:
                            st.info("No numeric columns selected for range validation.")

                    # Save the expectation suite
                    if st.button("Save Expectation Suite"):
                        # Create a dictionary to store the expectations
                        expectations = {
                            "name": suite_name,
                            "dataset": st.session_state.active_dataset,
                            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "columns": selected_columns,
                            "expectations": []
                        }

                        # Add column existence expectations
                        if st.session_state.expect_columns_to_exist:
                            expectations["expectations"].append({
                                "type": "expect_table_columns_to_contain_set",
                                "columns": selected_columns
                            })

                        # Add column type expectations
                        if st.session_state.expect_column_types:
                            for column in selected_columns:
                                dtype = str(quality_df[column].dtype)
                                expectations["expectations"].append({
                                    "type": "expect_column_values_to_be_of_type",
                                    "column": column,
                                    "type_": dtype
                                })

                        # Add not null expectations
                        for column in selected_columns:
                            if st.session_state.get(f"not_null_{column}", False):
                                expectations["expectations"].append({
                                    "type": "expect_column_values_to_not_be_null",
                                    "column": column
                                })

                        # Add uniqueness expectations
                        for column in selected_columns:
                            if st.session_state.get(f"unique_{column}", False):
                                expectations["expectations"].append({
                                    "type": "expect_column_values_to_be_unique",
                                    "column": column
                                })

                        # Add range expectations
                        numeric_columns = [col for col in selected_columns if quality_df[col].dtype.kind in 'biufc']
                        for column in numeric_columns:
                            if st.session_state.get(f"range_{column}", False):
                                min_range = st.session_state.get(f"range_slider_{column}")[0]
                                max_range = st.session_state.get(f"range_slider_{column}")[1]

                                expectations["expectations"].append({
                                    "type": "expect_column_values_to_be_between",
                                    "column": column,
                                    "min_value": min_range,
                                    "max_value": max_range
                                })

                        # Save the expectation suite to session state
                        st.session_state.expectation_suites[suite_name] = expectations

                        st.success(f"Expectation suite '{suite_name}' saved successfully!")
            
            with quality_tab2:
                st.subheader("Run Data Validations")

                # Check if there are any expectation suites defined
                if not st.session_state.expectation_suites:
                    st.warning("No expectation suites defined. Please create an expectation suite in the 'Define Expectations' tab first.")
                else:
                    # Get expectation suites for this dataset
                    dataset_suites = {name: suite for name, suite in st.session_state.expectation_suites.items()
                                     if suite["dataset"] == st.session_state.active_dataset}

                    if not dataset_suites:
                        st.warning(f"No expectation suites defined for dataset '{st.session_state.active_dataset}'. Please create an expectation suite first.")
                    else:
                        # Select an expectation suite
                        suite_options = list(dataset_suites.keys())
                        selected_suite = st.selectbox(
                            "Select an expectation suite to run:",
                            suite_options,
                            key="selected_validation_suite"
                        )

                        if selected_suite:
                            suite = dataset_suites[selected_suite]

                            # Display suite info
                            st.subheader("Expectation Suite Information")
                            st.markdown(f"""
                            - **Suite Name**: {suite['name']}
                            - **Created**: {suite['created_at']}
                            - **Number of Expectations**: {len(suite['expectations'])}
                            - **Columns Validated**: {len(suite['columns'])}
                            """)

                            # Run validation button
                            if st.button("Run Validation"):
                                with st.spinner("Running validation..."):
                                    # Create a validation result
                                    validation_id = f"{selected_suite}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                                    validation_results = {
                                        "id": validation_id,
                                        "suite_name": selected_suite,
                                        "dataset": st.session_state.active_dataset,
                                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                        "results": [],
                                        "summary": {
                                            "total_expectations": len(suite['expectations']),
                                            "passed_expectations": 0,
                                            "failed_expectations": 0,
                                            "success_percent": 0
                                        }
                                    }

                                    # Run each expectation
                                    for expectation in suite['expectations']:
                                        expectation_type = expectation['type']
                                        result = {
                                            "expectation_type": expectation_type,
                                            "success": False,
                                            "details": {}
                                        }

                                        # Validate based on expectation type using quality_df
                                        if expectation_type == "expect_table_columns_to_contain_set":
                                            columns_to_check = expectation['columns']
                                            missing_columns = [col for col in columns_to_check if col not in quality_df.columns]
                                            result["success"] = len(missing_columns) == 0
                                            result["details"] = {
                                                "columns_checked": columns_to_check,
                                                "missing_columns": missing_columns
                                            }

                                        elif expectation_type == "expect_column_values_to_be_of_type":
                                            column = expectation['column']
                                            expected_type = expectation['type_']
                                            actual_type = str(quality_df[column].dtype)
                                            result["success"] = expected_type == actual_type
                                            result["details"] = {
                                                "column": column,
                                                "expected_type": expected_type,
                                                "actual_type": actual_type
                                            }

                                        elif expectation_type == "expect_column_values_to_not_be_null":
                                            column = expectation['column']
                                            null_count = quality_df[column].isnull().sum()
                                            result["success"] = null_count == 0
                                            result["details"] = {
                                                "column": column,
                                                "null_count": int(null_count),
                                                "total_rows": quality_df.shape[0]
                                            }

                                        elif expectation_type == "expect_column_values_to_be_unique":
                                            column = expectation['column']
                                            unique_count = quality_df[column].nunique()
                                            total_count = quality_df.shape[0] - quality_df[column].isnull().sum()
                                            result["success"] = unique_count == total_count
                                            result["details"] = {
                                                "column": column,
                                                "unique_count": int(unique_count),
                                                "total_non_null_count": int(total_count)
                                            }
                                        
                                        elif expectation_type == "expect_column_values_to_be_between":
                                            column = expectation['column']
                                            min_value = expectation['min_value']
                                            max_value = expectation['max_value']
                                            
                                            # Check if all values are within the range (excluding nulls)
                                            col_data = quality_df[column].dropna()
                                            if len(col_data) > 0:
                                                min_actual = col_data.min()
                                                max_actual = col_data.max()
                                                result["success"] = min_actual >= min_value and max_actual <= max_value
                                            else:
                                                result["success"] = True  # No data to validate
                                            
                                            result["details"] = {
                                                "column": column,
                                                "expected_min": min_value,
                                                "expected_max": max_value,
                                                "actual_min": float(min_actual) if len(col_data) > 0 else None,
                                                "actual_max": float(max_actual) if len(col_data) > 0 else None,
                                                "non_null_count": len(col_data)
                                            }

                                        # Add result to validation results
                                        validation_results["results"].append(result)

                                        # Update summary
                                        if result["success"]:
                                            validation_results["summary"]["passed_expectations"] += 1
                                        else:
                                            validation_results["summary"]["failed_expectations"] += 1

                                    # Calculate success percentage
                                    total = validation_results["summary"]["total_expectations"]
                                    passed = validation_results["summary"]["passed_expectations"]
                                    validation_results["summary"]["success_percent"] = (passed / total * 100) if total > 0 else 0

                                    # Save validation results
                                    st.session_state.validation_results[validation_id] = validation_results

                                    # Display validation summary
                                    st.subheader("Validation Summary")

                                    # Create metrics for the summary
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.metric("Total Expectations", total)
                                    with col2:
                                        st.metric("Passed", passed)
                                    with col3:
                                        st.metric("Success Rate", f"{validation_results['summary']['success_percent']:.1f}%")

                                    # Display detailed results
                                    st.subheader("Detailed Results")

                                    # Create tabs for passed and failed expectations
                                    passed_tab, failed_tab = st.tabs(["✅ Passed", "❌ Failed"])

                                    with passed_tab:
                                        passed_results = [r for r in validation_results["results"] if r["success"]]
                                        if not passed_results:
                                            st.info("No expectations passed.")
                                        else:
                                            for i, result in enumerate(passed_results):
                                                with st.expander(f"{i+1}. {result['expectation_type']}"):
                                                    st.json(result["details"])

                                    with failed_tab:
                                        failed_results = [r for r in validation_results["results"] if not r["success"]]
                                        if not failed_results:
                                            st.success("All expectations passed!")
                                        else:
                                            for i, result in enumerate(failed_results):
                                                with st.expander(f"{i+1}. {result['expectation_type']}", expanded=True):
                                                    st.json(result["details"])
            
            with quality_tab3:
                st.subheader("Data Quality Reports")

                # Check if there are any validation results
                if not st.session_state.validation_results:
                    st.warning("No validation results available. Please run a validation in the 'Run Validations' tab first.")
                else:
                    # Filter validation results by dataset
                    filtered_results = {k: v for k, v in st.session_state.validation_results.items()
                                      if v["dataset"] == st.session_state.active_dataset}

                    if not filtered_results:
                        st.warning(f"No validation results available for dataset '{st.session_state.active_dataset}'.")
                    else:
                        # Display validation history
                        st.subheader("Validation History")

                        # Create a dataframe of validation results
                        history_data = []
                        for validation_id, validation in filtered_results.items():
                            history_data.append({
                                "Validation ID": validation_id,
                                "Suite Name": validation["suite_name"],
                                "Timestamp": validation["timestamp"],
                                "Total Expectations": validation["summary"]["total_expectations"],
                                "Passed": validation["summary"]["passed_expectations"],
                                "Failed": validation["summary"]["failed_expectations"],
                                "Success Rate": f"{validation['summary']['success_percent']:.1f}%"
                            })

                        # Convert to dataframe and display
                        if history_data:
                            history_df = pd.DataFrame(history_data)
                            st.dataframe(history_df, use_container_width=True)

                            # Select a validation to view details
                            selected_validation_id = st.selectbox(
                                "Select a validation to view details:",
                                list(filtered_results.keys()),
                                format_func=lambda x: f"{filtered_results[x]['suite_name']} ({filtered_results[x]['timestamp']})",
                                key="report_validation_select"
                            )

                            if selected_validation_id:
                                validation = filtered_results[selected_validation_id]

                                # Display validation details
                                st.subheader("Validation Details")
                                st.markdown(f"""
                                - **Dataset**: {validation['dataset']}
                                - **Suite Name**: {validation['suite_name']}
                                - **Timestamp**: {validation['timestamp']}
                                - **Total Expectations**: {validation['summary']['total_expectations']}
                                - **Passed**: {validation['summary']['passed_expectations']}
                                - **Failed**: {validation['summary']['failed_expectations']}
                                - **Success Rate**: {validation['summary']['success_percent']:.1f}%
                                """)

                                # Create visualization of validation results
                                st.subheader("Validation Results Visualization")

                                # Create a pie chart of passed/failed expectations
                                fig1 = go.Figure(data=[go.Pie(
                                    labels=['Passed', 'Failed'],
                                    values=[validation['summary']['passed_expectations'], validation['summary']['failed_expectations']],
                                    hole=.3,
                                    marker_colors=['#4CAF50', '#F44336']
                                )])
                                fig1.update_layout(title_text="Passed vs Failed Expectations")
                                st.plotly_chart(fig1, use_container_width=True)

                                # Create a bar chart of expectation types
                                expectation_types = {}
                                for result in validation['results']:
                                    exp_type = result['expectation_type']
                                    if exp_type not in expectation_types:
                                        expectation_types[exp_type] = {'passed': 0, 'failed': 0}

                                    if result["success"]:
                                        expectation_types[exp_type]['passed'] += 1
                                    else:
                                        expectation_types[exp_type]['failed'] += 1

                                # Prepare data for the bar chart
                                exp_names = list(expectation_types.keys())
                                passed_counts = [expectation_types[exp]['passed'] for exp in exp_names]
                                failed_counts = [expectation_types[exp]['failed'] for exp in exp_names]

                                # Create the bar chart
                                fig2 = go.Figure(data=[
                                    go.Bar(name='Passed', x=exp_names, y=passed_counts, marker_color='#4CAF50'),
                                    go.Bar(name='Failed', x=exp_names, y=failed_counts, marker_color='#F44336')
                                ])
                                fig2.update_layout(
                                    title_text='Results by Expectation Type',
                                    xaxis_title='Expectation Type',
                                    yaxis_title='Count',
                                    barmode='stack'
                                )
                                st.plotly_chart(fig2, use_container_width=True)

                                # Export options
                                st.subheader("Export Options")

                                # Export as JSON
                                if st.button("Export Validation Results as JSON"):
                                    # Create a deep copy of validation results to avoid modifying the original
                                    validation_copy = copy.deepcopy(validation)

                                    # Helper function to convert NumPy types to Python native types
                                    def convert_numpy_types(obj):
                                        if isinstance(obj, dict):
                                            return {k: convert_numpy_types(v) for k, v in obj.items()}
                                        elif isinstance(obj, list):
                                            return [convert_numpy_types(item) for item in obj]
                                        elif isinstance(obj, np.integer):
                                            return int(obj)
                                        elif isinstance(obj, np.floating):
                                            return float(obj)
                                        elif isinstance(obj, np.ndarray):
                                            return convert_numpy_types(obj.tolist())
                                        elif isinstance(obj, np.bool_):
                                            return bool(obj)
                                        else:
                                            return obj

                                    # Convert all NumPy types to Python native types
                                    validation_json_safe = convert_numpy_types(validation_copy)

                                    # Convert validation results to JSON
                                    json_results = json.dumps(validation_json_safe, indent=2)

                                    # Create a download link
                                    st.download_button(
                                        label="Download JSON",
                                        data=json_results,
                                        file_name=f"validation_{selected_validation_id}.json",
                                        mime="application/json"
                                    )

                                # Generate HTML report
                                if st.button("Generate HTML Report"):
                                    # Create an HTML report
                                    html_report = f"""
                                    <!DOCTYPE html>
                                    <html>
                                    <head>
                                        <title>Data Quality Report - {validation['dataset']}</title>
                                        <style>
                                            body {{ font-family: Arial, sans-serif; margin: 20px; }}
                                            h1, h2, h3 {{ color: #2C3E50; }}
                                            .summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                                            .passed {{ color: #4CAF50; }}
                                            .failed {{ color: #F44336; }}
                                            table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                                            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                                            th {{ background-color: #f2f2f2; }}
                                            tr:nth-child(even) {{ background-color: #f9f9f9; }}
                                        </style>
                                    </head>
                                    <body>
                                        <h1>Data Quality Report</h1>
                                        <div class="summary">
                                            <h2>Validation Summary</h2>
                                            <p><strong>Dataset:</strong> {validation['dataset']}</p>
                                            <p><strong>Suite Name:</strong> {validation['suite_name']}</p>
                                            <p><strong>Timestamp:</strong> {validation['timestamp']}</p>
                                            <p><strong>Total Expectations:</strong> {validation['summary']['total_expectations']}</p>
                                            <p><strong>Passed:</strong> <span class="passed">{validation['summary']['passed_expectations']}</span></p>
                                            <p><strong>Failed:</strong> <span class="failed">{validation['summary']['failed_expectations']}</span></p>
                                            <p><strong>Success Rate:</strong> {validation['summary']['success_percent']:.1f}%</p>
                                        </div>

                                        <h2>Detailed Results</h2>
                                        <table>
                                            <tr>
                                                <th>#</th>
                                                <th>Expectation Type</th>
                                                <th>Status</th>
                                                <th>Details</th>
                                            </tr>
                                    """

                                    # Add rows for each expectation result
                                    for i, result in enumerate(validation['results']):
                                        status = '<span class="passed">Passed</span>' if result['success'] else '<span class="failed">Failed</span>'
                                        # Convert NumPy types to Python native types before JSON serialization
                                        json_safe_details = convert_numpy_types(result['details'])
                                        details = json.dumps(json_safe_details, indent=2).replace('\n', '<br>').replace(' ', '&nbsp;')
                                        html_report += f"""
                                            <tr>
                                                <td>{i+1}</td>
                                                <td>{result['expectation_type']}</td>
                                                <td>{status}</td>
                                                <td><pre>{details}</pre></td>
                                            </tr>
                                        """

                                    # Close the HTML
                                    html_report += """
                                        </table>
                                        <p><em>Report generated by Data Quality Check application</em></p>
                                    </body>
                                    </html>
                                    """

                                    # Create a download link
                                    st.download_button(
                                        label="Download HTML Report",
                                        data=html_report,
                                        file_name=f"quality_report_{selected_validation_id}.html",
                                        mime="text/html"
                                    )