#!/usr/bin/env python3
"""
Test script for irregular time series data handling
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys

# Add current directory to path
sys.path.append('.')

def test_irregular_data():
    """Test handling of irregular time series data"""
    print("🧪 Testing Irregular Time Series Data Handling")
    print("=" * 50)
    
    try:
        from app_core.utils.time_series import (
            analyze_time_series_frequency, suggest_frequency_fix, prepare_darts_timeseries
        )
        
        # Create irregular time series data (like the user's data)
        dates = []
        current_date = datetime(2023, 1, 1)
        for i in range(50):
            dates.append(current_date)
            # Add irregular gaps: sometimes 1 day, sometimes 2-3 days
            gap = np.random.choice([1, 2, 3], p=[0.7, 0.2, 0.1])
            current_date += timedelta(days=gap)

        values = np.random.randn(50).cumsum() + 100
        df = pd.DataFrame({'date': dates, 'value': values})

        print('📊 Created irregular time series data...')
        print(f'Data shape: {df.shape}')
        print(f'Date range: {df["date"].min()} to {df["date"].max()}')
        
        # Show some date differences
        date_diffs = df['date'].diff().dropna()
        unique_diffs = date_diffs.unique()
        print(f'Unique time gaps: {[str(d) for d in unique_diffs[:5]]}')

        # Test frequency analysis
        print('\n🔍 Testing frequency analysis...')
        freq_analysis = analyze_time_series_frequency(df, 'date')
        
        if 'error' not in freq_analysis:
            print(f'✅ Frequency Analysis Results:')
            print(f'   - Is regular: {freq_analysis["is_regular"]}')
            print(f'   - Regularity: {freq_analysis["regularity_percentage"]:.1f}%')
            print(f'   - Suggested frequency: {freq_analysis["suggested_freq"]}')
            print(f'   - Most common gap: {freq_analysis["most_common_diff"]}')
            
            # Test suggestion
            suggestion = suggest_frequency_fix(freq_analysis)
            print(f'\n💡 Suggestion preview: {suggestion[:150]}...')
        else:
            print(f'❌ Frequency analysis failed: {freq_analysis["error"]}')

        # Test TimeSeries creation
        print('\n📈 Testing TimeSeries creation...')
        ts = prepare_darts_timeseries(df, 'date', 'value')

        if ts is not None:
            print(f'✅ TimeSeries created successfully!')
            print(f'   - Length: {len(ts)}')
            print(f'   - Start: {ts.start_time()}')
            print(f'   - End: {ts.end_time()}')
            print(f'   - Frequency: {ts.freq}')
            
            # Test simple forecasting
            try:
                from darts.models import NaiveSeasonal
                model = NaiveSeasonal(K=7)
                model.fit(ts[:40])
                forecast = model.predict(5)
                print(f'✅ Forecasting test successful: {len(forecast)} predictions')
            except Exception as e:
                print(f'⚠️ Forecasting test failed: {str(e)}')
        else:
            print('❌ TimeSeries creation failed')

        print('\n🎉 Irregular data handling test completed!')
        return True
        
    except Exception as e:
        print(f'❌ Test failed with error: {str(e)}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_irregular_data()
    if success:
        print("\n✅ Irregular data handling is working!")
    else:
        print("\n❌ Issues found with irregular data handling.")
    
    sys.exit(0 if success else 1)
