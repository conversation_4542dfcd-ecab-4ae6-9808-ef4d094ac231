FROM python:3.11-slim

WORKDIR /app

# Install system dependencies including certificates, build tools, and Oracle client
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    openssl \
    unzip \
    libaio1 \
    wget \
    build-essential \
    gcc \
    g++ \
    && update-ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Oracle Instant Client
RUN mkdir -p /opt/oracle && \
    cd /opt/oracle && \
    wget https://download.oracle.com/otn_software/linux/instantclient/2118000/instantclient-basic-linux.x64-*********.0dbru.zip && \
    unzip instantclient-basic-linux.x64-*********.0dbru.zip && \
    rm instantclient-basic-linux.x64-*********.0dbru.zip && \
    echo '/opt/oracle/instantclient_21_18' > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

# Set environment variables to handle SSL certificate issues and Oracle
ENV PYTHONHTTPSVERIFY=0
ENV PIP_DISABLE_PIP_VERSION_CHECK=1
ENV OPENAI_PYTHON_LIBRARY_NO_VERIFY_SSL=true
ENV REQUESTS_CA_BUNDLE="/app/certs/cacert.pem"
ENV SSL_CERT_FILE="/app/certs/cacert.pem"
ENV NODE_TLS_REJECT_UNAUTHORIZED=0
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_21_18:$LD_LIBRARY_PATH
ENV ORACLE_HOME=/opt/oracle/instantclient_21_18
ENV TNS_ADMIN=/opt/oracle/instantclient_21_18/network/admin

# Copy requirements first for better caching
COPY requirements.txt .

# Use multi-stage build for better layer caching
RUN pip install --timeout 180 --no-cache-dir --trusted-host pypi.org --trusted-host files.pythonhosted.org --trusted-host pypi.python.org -r requirements.txt

# Copy the application code
COPY . .

# Copy Oracle wallet files to instantclient network admin directory
RUN mkdir -p /opt/oracle/instantclient_21_18/network/admin && \
    cp /app/wallet/cwallet.sso /opt/oracle/instantclient_21_18/network/admin/ && \
    cp /app/wallet/tnsnames.ora /opt/oracle/instantclient_21_18/network/admin/ && \
    cp /app/wallet/sqlnet.ora /opt/oracle/instantclient_21_18/network/admin/ 
    
# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/exports/charts /app/dataset_storage/cache \
    /app/dataset_storage/datasets /app/dataset_storage/metadata \
    /bin/ && \
    chmod -R 755 /app/logs /app/exports /app/dataset_storage

# Create directory, copy script and set permissions
COPY docker-entrypoint.sh /bin/
RUN chmod +x /bin/docker-entrypoint.sh

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV STREAMLIT_SERVER_PORT=8000
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# Expose the port Streamlit runs on
EXPOSE 8000

# Command to run the application
CMD ["streamlit", "run", "Welcome.py", "--server.port=8000", "--server.address=0.0.0.0"]
