import streamlit as st
from app_core.config import init_session_state
import sys
import os

# Add the parent directory to sys.path to import utils
from app_core.utils.styling import apply_css, get_color
from app_core.auth.auth_config import AuthConfig


st.set_page_config(
    layout='wide',
    page_title="Welcome!",
    page_icon="🐼"
)

# Initialize session state only (not LLM - we don't need it for the welcome page)
init_session_state()

# Initialize authentication
auth = AuthConfig()

# Apply centralized CSS (much more efficient than inline CSS)
apply_css()

# Get theme colors for use in inline styles
primary_color = get_color('primary')
secondary_color = get_color('secondary')
background_color = get_color('background')
text_color = get_color('text')
accent_color = get_color('accent')
gray_light = get_color('gray_light')

# Add custom CSS for this page with theme colors
st.markdown(f"""
<style>
.hero-container {{
    background: linear-gradient(135deg, {secondary_color} 0%, {get_color('secondary')} 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}}

.hero-title {{
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}}

.hero-subtitle {{
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 1000px;
    margin: 0 auto;
    line-height: 1.6;
}}

.feature-card {{
    background-color: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    height: 100%;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    border-top: 5px solid {secondary_color};
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    position: relative;
    z-index: 0;
    overflow: hidden;
}}

.feature-card:hover {{
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}}

.feature-card-locked {{
    background-color: #f8f9fa;
    border-top-color: #6c757d;
    opacity: 0.8;
}}

.feature-card-locked:hover {{
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}}

/* Add consistent styling for dataset view buttons */
.dataset-view-button {{
    background-color: {secondary_color} !important;
    color: white !important;
    font-weight: 500 !important;
    border-radius: 5px;
    border: none !important;
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease;
    width: 100%;
}}

.dataset-view-button:hover {{
    background-color: {get_color('secondary')} !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}}

.feature-content {{
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}}

.feature-icon {{
    font-size: 3rem;
    margin-bottom: 1rem;
    color: {secondary_color};
    transition: all 0.3s ease;
}}

.feature-title {{
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: {text_color};
    transition: all 0.3s ease;
}}

.feature-list {{
    color: {get_color('gray_dark')};
    padding-left: 1.5rem;
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    text-align: left;
}}

.feature-card:hover .feature-list {{
    opacity: 1;
    max-height: 300px;
    margin-top: 1rem;
    margin-bottom: 1rem;
}}

.feature-card:hover .feature-icon {{
    font-size: 2rem;
    margin-bottom: 0.5rem;
}}

.feature-card:hover .feature-content {{
    align-items: flex-start;
    justify-content: flex-start;
    text-align: left;
}}

.feature-list li {{
    margin-bottom: 0.5rem;
}}

/* Style for "Get Started" section */
.get-started-container {{
    background-color: {gray_light};
    padding: 2rem;
    border-radius: 10px;
    margin-top: 2rem;
    text-align: center;
}}

.get-started-title {{
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: {text_color};
}}

.get-started-subtitle {{
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: {get_color('gray_dark')};
}}

.big-button {{
    padding: 0.8rem 1.5rem;
    font-size: 1.2rem;
    background-color: {secondary_color};
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}}

.big-button:hover {{
    background-color: {get_color('secondary')};
}}

.footer {{
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
    text-align: center;
    color: {get_color('gray_dark')};
}}

/* Style for the buttons to make them blend with the cards */
div[data-testid="stButton"] {{
    margin-top: 10px;
    opacity: 0.8;
    transition: all 0.3s ease;
}}

div[data-testid="stButton"]:hover {{
    opacity: 1;
}}

div[data-testid="stButton"] button {{
    width: 100%;
    background-color: {secondary_color} !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease;
}}

div[data-testid="stButton"] button:hover {{
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    background-color: {get_color('secondary')} !important;
}}
</style>
""", unsafe_allow_html=True)

# Hero section with gradient background
st.markdown("""
<div class="hero-container">
    <div class="hero-title">👋Welcome to Data Insight!</div>
    <div class="hero-subtitle">
        An application that helps you analyze, visualize, and extract insights from your data.
    </div>
</div>
""", unsafe_allow_html=True)

# Show authentication status and controls
if auth.is_authenticated():
    user_info = auth.get_current_user()
    st.success(f"✅ Welcome back, {user_info['full_name']}! You are logged in as {user_info['role'].title()}.")
    
    # Show user's schema access
    schemas = user_info['schema_permissions']
    if "*" in schemas:
        st.info("🌟 You have access to all Oracle schemas")
    else:
        st.info(f"📋 You have access to these Oracle schemas: {', '.join(schemas)}")
    
    # Logout button
    col1, col2, col3 = st.columns([2, 1, 2])
    with col2:
        if st.button("🚪 Logout", use_container_width=True):
            auth.logout()
            st.rerun()
else:
    st.warning("🔒 You are not logged in. Some features require authentication.")
    
    # Login button
    col1, col2, col3 = st.columns([2, 1, 2])
    with col2:
        if st.button("🔐 Login", use_container_width=True, type="primary"):
            st.switch_page("pages/0_Login.py")

# Section header
st.markdown("## What can you do with this app?", unsafe_allow_html=True)

# Create a responsive layout with feature cards
col1, col2 = st.columns([1, 1])

with col1:
    # Upload Data card
    with st.container():
        # Make the card clickable by wrapping it in a link
        st.markdown("""
        <div class="feature-card" onclick="parent.open('1_Upload_Data.py', '_self');">
            <div class="feature-content">
                <div class="feature-icon">📤</div>
                <div class="feature-title">Upload Data</div>
                <ul class="feature-list">
                    <li>Upload your CSV or Excel files</li>
                    <li>Configure import options</li>
                    <li>Manage multiple datasets</li>
                    <li>Preview your data before analysis</li>
                </ul>
            </div>
        </div>
        """, unsafe_allow_html=True)

with col2:
    # Data Explorer card
    with st.container():
        st.markdown("""
        <div class="feature-card" onclick="parent.open('2_Data_Explorer.py', '_self');">
            <div class="feature-content">
                <div class="feature-icon">📊</div>
                <div class="feature-title">Explore Visually</div>
                <ul class="feature-list">
                    <li>Interactive data visualization</li>
                    <li>Drag-and-drop chart builder</li>
                    <li>Generate data profiles automatically</li>
                    <li>Check data quality and expectations</li>
                    <li>Identify issues and outliers</li>
                </ul>
            </div>
        </div>
        """, unsafe_allow_html=True)

# Second row of feature cards
col3, col4 = st.columns([1, 1])

with col3:
    # Chat card
    with st.container():
        st.markdown("""
        <div class="feature-card" onclick="parent.open('3_Chat.py', '_self');">
            <div class="feature-content">
                <div class="feature-icon">💬</div>
                <div class="feature-title">Chat with your data</div>
                <ul class="feature-list">
                    <li>Ask questions in natural language</li>
                    <li>Get visualizations and insights</li>
                    <li>View the generated code</li>
                    <li>Work with multiple datasets</li>
                </ul>
            </div>
        </div>
        """, unsafe_allow_html=True)

with col4:
    # Database Connections card
    with st.container():
        # Check if user is authenticated for database access
        if auth.is_authenticated():
            onclick_action = "parent.open('3_Database_Connections.py', '_self');"
            card_class = "feature-card"
            lock_text = ""
        else:
            onclick_action = "parent.open('0_Login.py', '_self');"
            card_class = "feature-card"
            lock_text = "<li style='color: #ff6b6b;'>🔒 Login required</li>"
        
        st.markdown(f"""
        <div class="{card_class}" onclick="{onclick_action}">
            <div class="feature-content">
                <div class="feature-icon">🗄️</div>
                <div class="feature-title">Database Connections</div>
                <ul class="feature-list">
                    <li>Connect to Oracle databases</li>
                    <li>Browse schemas and tables</li>
                    <li>Import datasets for analysis</li>
                    <li>Role-based schema access</li>
                    {lock_text}
                </ul>
            </div>
        </div>
        """, unsafe_allow_html=True)

# Third row of feature cards
col5, col6 = st.columns([1, 1])

with col5:
    # Time Series Analysis card
    with st.container():
        st.markdown("""
        <div class="feature-card" onclick="parent.open('5_Time_Series_Analysis.py', '_self');">
            <div class="feature-content">
                <div class="feature-icon">📈</div>
                <div class="feature-title">Time Series Analysis</div>
                <ul class="feature-list">
                    <li>Advanced forecasting with Darts framework</li>
                    <li>Multiple models: ARIMA, Prophet, Neural Networks</li>
                    <li>Trend and seasonality analysis</li>
                    <li>Interactive forecast visualizations</li>
                    <li>Model comparison and evaluation</li>
                </ul>
            </div>
        </div>
        """, unsafe_allow_html=True)

with col6:
    # Placeholder for future feature
    with st.container():
        st.markdown("""
        <div class="feature-card" style="opacity: 0.6; cursor: not-allowed;">
            <div class="feature-content">
                <div class="feature-icon">🚀</div>
                <div class="feature-title">More Features Coming Soon</div>
                <ul class="feature-list">
                    <li>Advanced analytics</li>
                    <li>Machine learning models</li>
                    <li>Automated reporting</li>
                    <li>Data pipelines</li>
                </ul>
            </div>
        </div>
        """, unsafe_allow_html=True)

# Add a Get Started section
st.markdown("""
<div class="get-started-container">
    <div class="get-started-title">Ready to start?</div>
    <div class="get-started-subtitle">Upload your data and begin exploring insights in seconds</div>
</div>
""", unsafe_allow_html=True)

# Create a big centered button to upload data
col_left, col_center, col_right = st.columns([1, 2, 1])
with col_center:
    if st.button("Upload Your First Dataset", use_container_width=True, key="big_upload_btn"):
        st.switch_page("pages/1_Upload_Data.py")
