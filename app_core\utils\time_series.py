"""
Time Series Analysis Utilities for Darts Integration
"""
import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional, Any
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import streamlit as st

logger = logging.getLogger(__name__)

def detect_datetime_columns(df: pd.DataFrame) -> List[str]:
    """
    Detect potential datetime columns in a DataFrame.
    
    Args:
        df: Input DataFrame
        
    Returns:
        List of column names that could be datetime columns
    """
    datetime_columns = []
    
    # Check for explicit datetime columns
    datetime_columns.extend(df.select_dtypes(include=['datetime64']).columns.tolist())
    
    # Check for potential datetime columns in object/string columns
    for col in df.select_dtypes(include=['object', 'string']).columns:
        if df[col].dtype == 'object':
            # Sample a few non-null values to check if they could be dates
            sample_values = df[col].dropna().head(10)
            if len(sample_values) > 0:
                try:
                    # Try to parse as datetime
                    pd.to_datetime(sample_values, errors='raise')
                    datetime_columns.append(col)
                except:
                    # Check if column name suggests it's a date
                    col_lower = col.lower()
                    if any(keyword in col_lower for keyword in ['date', 'time', 'timestamp', 'created', 'updated']):
                        datetime_columns.append(col)
    
    return list(set(datetime_columns))

def detect_numeric_columns(df: pd.DataFrame) -> List[str]:
    """
    Detect numeric columns suitable for time series analysis.
    
    Args:
        df: Input DataFrame
        
    Returns:
        List of numeric column names
    """
    return df.select_dtypes(include=[np.number]).columns.tolist()

def validate_time_series_data(df: pd.DataFrame, date_col: str, value_cols: List[str]) -> Tuple[bool, str, pd.DataFrame]:
    """
    Validate and prepare data for time series analysis.
    
    Args:
        df: Input DataFrame
        date_col: Name of the datetime column
        value_cols: List of value column names
        
    Returns:
        Tuple of (is_valid, message, cleaned_dataframe)
    """
    try:
        # Create a copy to avoid modifying original data
        df_clean = df.copy()
        
        # Convert date column to datetime
        if df_clean[date_col].dtype != 'datetime64[ns]':
            df_clean[date_col] = pd.to_datetime(df_clean[date_col])
        
        # Check for missing dates
        missing_dates = df_clean[date_col].isnull().sum()
        if missing_dates > 0:
            return False, f"Found {missing_dates} missing dates in {date_col}", df_clean
        
        # Check for missing values in target columns
        for col in value_cols:
            if col not in df_clean.columns:
                return False, f"Column '{col}' not found in dataset", df_clean
            
            missing_values = df_clean[col].isnull().sum()
            if missing_values > len(df_clean) * 0.5:  # More than 50% missing
                return False, f"Column '{col}' has too many missing values ({missing_values}/{len(df_clean)})", df_clean
        
        # Sort by date
        df_clean = df_clean.sort_values(date_col).reset_index(drop=True)
        
        # Check for minimum data points
        if len(df_clean) < 10:
            return False, f"Insufficient data points ({len(df_clean)}). Need at least 10 for time series analysis.", df_clean
        
        return True, "Data validation successful", df_clean
        
    except Exception as e:
        return False, f"Validation error: {str(e)}", df

def create_time_series_plot(df: pd.DataFrame, date_col: str, value_cols: List[str], title: str = "Time Series") -> go.Figure:
    """
    Create an interactive time series plot using Plotly.
    
    Args:
        df: DataFrame with time series data
        date_col: Name of the datetime column
        value_cols: List of value columns to plot
        title: Plot title
        
    Returns:
        Plotly figure
    """
    fig = go.Figure()
    
    colors = px.colors.qualitative.Set1
    
    for i, col in enumerate(value_cols):
        fig.add_trace(go.Scatter(
            x=df[date_col],
            y=df[col],
            mode='lines',
            name=col,
            line=dict(color=colors[i % len(colors)]),
            hovertemplate=f'<b>{col}</b><br>Date: %{{x}}<br>Value: %{{y}}<extra></extra>'
        ))
    
    fig.update_layout(
        title=title,
        xaxis_title="Date",
        yaxis_title="Value",
        hovermode='x unified',
        showlegend=True,
        height=500
    )
    
    return fig

def prepare_darts_timeseries(df: pd.DataFrame, date_col: str, value_col: str, freq: Optional[str] = None):
    """
    Prepare data for Darts TimeSeries object.
    
    Args:
        df: Input DataFrame
        date_col: Name of the datetime column
        value_col: Name of the value column
        freq: Frequency string (e.g., 'D', 'M', 'H')
        
    Returns:
        Darts TimeSeries object
    """
    try:
        from darts import TimeSeries
        
        # Prepare the data
        df_prep = df[[date_col, value_col]].copy()
        df_prep = df_prep.dropna()
        df_prep = df_prep.sort_values(date_col)
        
        # Create TimeSeries
        ts = TimeSeries.from_dataframe(
            df_prep,
            time_col=date_col,
            value_cols=value_col,
            freq=freq
        )
        
        return ts
        
    except ImportError:
        st.error("Darts library not available. Please install it with: pip install darts[all]")
        return None
    except Exception as e:
        st.error(f"Error creating TimeSeries: {str(e)}")
        return None

def get_time_series_info(df: pd.DataFrame, date_col: str, value_cols: List[str]) -> Dict[str, Any]:
    """
    Get comprehensive information about the time series data.
    
    Args:
        df: Input DataFrame
        date_col: Name of the datetime column
        value_cols: List of value columns
        
    Returns:
        Dictionary with time series information
    """
    info = {}
    
    try:
        # Basic info
        info['total_observations'] = len(df)
        info['date_range'] = {
            'start': df[date_col].min(),
            'end': df[date_col].max(),
            'span_days': (df[date_col].max() - df[date_col].min()).days
        }
        
        # Frequency analysis
        date_diffs = df[date_col].diff().dropna()
        if len(date_diffs) > 0:
            most_common_diff = date_diffs.mode().iloc[0] if len(date_diffs.mode()) > 0 else date_diffs.median()
            info['inferred_frequency'] = str(most_common_diff)
            info['is_regular'] = len(date_diffs.unique()) == 1
        
        # Value columns info
        info['value_columns'] = {}
        for col in value_cols:
            col_info = {
                'missing_values': df[col].isnull().sum(),
                'missing_percentage': (df[col].isnull().sum() / len(df)) * 100,
                'min': df[col].min(),
                'max': df[col].max(),
                'mean': df[col].mean(),
                'std': df[col].std()
            }
            info['value_columns'][col] = col_info
        
        return info
        
    except Exception as e:
        logger.error(f"Error getting time series info: {str(e)}")
        return {'error': str(e)}

def suggest_forecast_horizon(df: pd.DataFrame, date_col: str) -> int:
    """
    Suggest an appropriate forecast horizon based on data characteristics.
    
    Args:
        df: Input DataFrame
        date_col: Name of the datetime column
        
    Returns:
        Suggested forecast horizon (number of periods)
    """
    total_periods = len(df)
    
    # Suggest 10-20% of the data length, with reasonable bounds
    suggested = max(5, min(50, int(total_periods * 0.15)))
    
    return suggested
