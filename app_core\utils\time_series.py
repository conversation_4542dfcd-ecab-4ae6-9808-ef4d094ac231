"""
Time Series Analysis Utilities for Darts Integration
"""
import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional, Any
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import streamlit as st

logger = logging.getLogger(__name__)

def detect_datetime_columns(df: pd.DataFrame) -> List[str]:
    """
    Detect potential datetime columns in a DataFrame.
    
    Args:
        df: Input DataFrame
        
    Returns:
        List of column names that could be datetime columns
    """
    datetime_columns = []
    
    # Check for explicit datetime columns
    datetime_columns.extend(df.select_dtypes(include=['datetime64']).columns.tolist())
    
    # Check for potential datetime columns in object/string columns
    for col in df.select_dtypes(include=['object', 'string']).columns:
        if df[col].dtype == 'object':
            # Sample a few non-null values to check if they could be dates
            sample_values = df[col].dropna().head(10)
            if len(sample_values) > 0:
                try:
                    # Try to parse as datetime with common formats first
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        pd.to_datetime(sample_values, errors='raise', infer_datetime_format=True)
                    datetime_columns.append(col)
                except:
                    # Check if column name suggests it's a date
                    col_lower = col.lower()
                    if any(keyword in col_lower for keyword in ['date', 'time', 'timestamp', 'created', 'updated']):
                        datetime_columns.append(col)
    
    return list(set(datetime_columns))

def detect_numeric_columns(df: pd.DataFrame) -> List[str]:
    """
    Detect numeric columns suitable for time series analysis.
    
    Args:
        df: Input DataFrame
        
    Returns:
        List of numeric column names
    """
    return df.select_dtypes(include=[np.number]).columns.tolist()

def validate_time_series_data(df: pd.DataFrame, date_col: str, value_cols: List[str]) -> Tuple[bool, str, pd.DataFrame]:
    """
    Validate and prepare data for time series analysis.
    
    Args:
        df: Input DataFrame
        date_col: Name of the datetime column
        value_cols: List of value column names
        
    Returns:
        Tuple of (is_valid, message, cleaned_dataframe)
    """
    try:
        # Create a copy to avoid modifying original data
        df_clean = df.copy()
        
        # Convert date column to datetime
        if df_clean[date_col].dtype != 'datetime64[ns]':
            import warnings
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                df_clean[date_col] = pd.to_datetime(df_clean[date_col], infer_datetime_format=True)
        
        # Check for missing dates
        missing_dates = df_clean[date_col].isnull().sum()
        if missing_dates > 0:
            return False, f"Found {missing_dates} missing dates in {date_col}", df_clean
        
        # Check for missing values in target columns
        for col in value_cols:
            if col not in df_clean.columns:
                return False, f"Column '{col}' not found in dataset", df_clean
            
            missing_values = df_clean[col].isnull().sum()
            if missing_values > len(df_clean) * 0.5:  # More than 50% missing
                return False, f"Column '{col}' has too many missing values ({missing_values}/{len(df_clean)})", df_clean
        
        # Sort by date
        df_clean = df_clean.sort_values(date_col).reset_index(drop=True)
        
        # Check for minimum data points
        if len(df_clean) < 10:
            return False, f"Insufficient data points ({len(df_clean)}). Need at least 10 for time series analysis.", df_clean
        
        return True, "Data validation successful", df_clean
        
    except Exception as e:
        return False, f"Validation error: {str(e)}", df

def create_time_series_plot(df: pd.DataFrame, date_col: str, value_cols: List[str], title: str = "Time Series") -> go.Figure:
    """
    Create an interactive time series plot using Plotly.
    
    Args:
        df: DataFrame with time series data
        date_col: Name of the datetime column
        value_cols: List of value columns to plot
        title: Plot title
        
    Returns:
        Plotly figure
    """
    fig = go.Figure()
    
    colors = px.colors.qualitative.Set1
    
    for i, col in enumerate(value_cols):
        fig.add_trace(go.Scatter(
            x=df[date_col],
            y=df[col],
            mode='lines',
            name=col,
            line=dict(color=colors[i % len(colors)]),
            hovertemplate=f'<b>{col}</b><br>Date: %{{x}}<br>Value: %{{y}}<extra></extra>'
        ))
    
    fig.update_layout(
        title=title,
        xaxis_title="Date",
        yaxis_title="Value",
        hovermode='x unified',
        showlegend=True,
        height=500
    )
    
    return fig

def prepare_darts_timeseries(df: pd.DataFrame, date_col: str, value_col: str, freq: Optional[str] = None):
    """
    Prepare data for Darts TimeSeries object.

    Args:
        df: Input DataFrame
        date_col: Name of the datetime column
        value_col: Name of the value column
        freq: Frequency string (e.g., 'D', 'M', 'H')

    Returns:
        Darts TimeSeries object
    """
    try:
        from darts import TimeSeries

        # Prepare the data
        df_prep = df[[date_col, value_col]].copy()
        df_prep = df_prep.dropna()
        df_prep = df_prep.sort_values(date_col)

        # Ensure datetime column is properly formatted
        df_prep[date_col] = pd.to_datetime(df_prep[date_col])

        # Handle irregular time series data
        if freq is None:
            # Analyze the time differences to determine the best approach
            date_diffs = df_prep[date_col].diff().dropna()

            if len(date_diffs) > 0:
                # Get unique time differences
                unique_diffs = date_diffs.unique()

                # Check if we have a regular pattern
                if len(unique_diffs) == 1:
                    # Regular frequency - convert to frequency string
                    diff = unique_diffs[0]
                    if diff.days >= 365:
                        freq = 'Y'
                    elif diff.days >= 28:
                        freq = 'M'
                    elif diff.days >= 7:
                        freq = 'W'
                    elif diff.days >= 1:
                        freq = 'D'
                    elif diff.seconds >= 3600:
                        freq = 'H'
                    else:
                        freq = 'T'  # Minutes
                else:
                    # Irregular frequency - find the most common difference
                    most_common_diff = date_diffs.mode().iloc[0] if len(date_diffs.mode()) > 0 else date_diffs.median()

                    # Use the most common difference as base frequency
                    if most_common_diff.days >= 1:
                        freq = 'D'  # Default to daily for irregular daily data
                    elif most_common_diff.seconds >= 3600:
                        freq = 'H'
                    else:
                        freq = 'T'

        # Try multiple approaches to create TimeSeries
        ts = None
        error_messages = []

        # Approach 1: Try with inferred frequency and fill missing dates
        if freq and ts is None:
            try:
                ts = TimeSeries.from_dataframe(
                    df_prep,
                    time_col=date_col,
                    value_cols=value_col,
                    freq=freq,
                    fill_missing_dates=True
                )
            except Exception as e:
                error_messages.append(f"Approach 1 failed: {str(e)}")

        # Approach 2: Try without frequency, let Darts infer with fill_missing_dates
        if ts is None:
            try:
                ts = TimeSeries.from_dataframe(
                    df_prep,
                    time_col=date_col,
                    value_cols=value_col,
                    fill_missing_dates=True,
                    freq=None
                )
            except Exception as e:
                error_messages.append(f"Approach 2 failed: {str(e)}")

        # Approach 3: Resample to regular frequency first
        if ts is None and freq:
            try:
                # Resample the data to regular frequency
                df_resampled = df_prep.set_index(date_col).resample(freq).mean().reset_index()
                df_resampled = df_resampled.dropna()

                ts = TimeSeries.from_dataframe(
                    df_resampled,
                    time_col=date_col,
                    value_cols=value_col,
                    freq=freq
                )
            except Exception as e:
                error_messages.append(f"Approach 3 failed: {str(e)}")

        # Approach 4: Force daily frequency for irregular data
        if ts is None:
            try:
                ts = TimeSeries.from_dataframe(
                    df_prep,
                    time_col=date_col,
                    value_cols=value_col,
                    freq='D',
                    fill_missing_dates=True
                )
            except Exception as e:
                error_messages.append(f"Approach 4 failed: {str(e)}")

        # If all approaches failed, show detailed error
        if ts is None:
            error_msg = "Failed to create TimeSeries with multiple approaches:\n" + "\n".join(error_messages)
            st.error(error_msg)
            logger.error(error_msg)

        return ts

    except ImportError:
        st.error("Darts library not available. Please install it with: pip install darts[all]")
        return None
    except Exception as e:
        st.error(f"Error creating TimeSeries: {str(e)}")
        logger.error(f"Error creating TimeSeries: {str(e)}")
        return None

def get_time_series_info(df: pd.DataFrame, date_col: str, value_cols: List[str]) -> Dict[str, Any]:
    """
    Get comprehensive information about the time series data.
    
    Args:
        df: Input DataFrame
        date_col: Name of the datetime column
        value_cols: List of value columns
        
    Returns:
        Dictionary with time series information
    """
    info = {}
    
    try:
        # Basic info
        info['total_observations'] = len(df)
        info['date_range'] = {
            'start': df[date_col].min(),
            'end': df[date_col].max(),
            'span_days': (df[date_col].max() - df[date_col].min()).days
        }
        
        # Frequency analysis
        date_diffs = df[date_col].diff().dropna()
        if len(date_diffs) > 0:
            most_common_diff = date_diffs.mode().iloc[0] if len(date_diffs.mode()) > 0 else date_diffs.median()
            info['inferred_frequency'] = str(most_common_diff)
            info['is_regular'] = len(date_diffs.unique()) == 1
        
        # Value columns info
        info['value_columns'] = {}
        for col in value_cols:
            col_info = {
                'missing_values': df[col].isnull().sum(),
                'missing_percentage': (df[col].isnull().sum() / len(df)) * 100,
                'min': df[col].min(),
                'max': df[col].max(),
                'mean': df[col].mean(),
                'std': df[col].std()
            }
            info['value_columns'][col] = col_info
        
        return info
        
    except Exception as e:
        logger.error(f"Error getting time series info: {str(e)}")
        return {'error': str(e)}

def suggest_forecast_horizon(df: pd.DataFrame, date_col: str = None) -> int:
    """
    Suggest an appropriate forecast horizon based on data characteristics.

    Args:
        df: Input DataFrame
        date_col: Name of the datetime column (optional, for future use)

    Returns:
        Suggested forecast horizon (number of periods)
    """
    total_periods = len(df)

    # Suggest 10-20% of the data length, with reasonable bounds
    suggested = max(5, min(50, int(total_periods * 0.15)))

    return suggested

def analyze_time_series_frequency(df: pd.DataFrame, date_col: str) -> Dict[str, Any]:
    """
    Analyze the frequency pattern of a time series to help users understand irregularities.

    Args:
        df: Input DataFrame
        date_col: Name of the datetime column

    Returns:
        Dictionary with frequency analysis results
    """
    try:
        # Ensure datetime column
        df_temp = df.copy()
        df_temp[date_col] = pd.to_datetime(df_temp[date_col])
        df_temp = df_temp.sort_values(date_col)

        # Calculate time differences
        date_diffs = df_temp[date_col].diff().dropna()

        if len(date_diffs) == 0:
            return {'error': 'Insufficient data for frequency analysis'}

        # Analyze differences
        unique_diffs = date_diffs.unique()
        diff_counts = date_diffs.value_counts()

        # Convert to days for easier interpretation
        diff_days = {str(diff): diff.days + diff.seconds/86400 for diff in unique_diffs}

        # Determine if regular or irregular
        is_regular = len(unique_diffs) == 1

        # Find most common frequency
        most_common_diff = diff_counts.index[0]
        most_common_count = diff_counts.iloc[0]

        # Suggest frequency string
        if most_common_diff.days >= 365:
            suggested_freq = 'Y'
            freq_name = 'Yearly'
        elif most_common_diff.days >= 28:
            suggested_freq = 'M'
            freq_name = 'Monthly'
        elif most_common_diff.days >= 7:
            suggested_freq = 'W'
            freq_name = 'Weekly'
        elif most_common_diff.days >= 1:
            suggested_freq = 'D'
            freq_name = 'Daily'
        elif most_common_diff.seconds >= 3600:
            suggested_freq = 'H'
            freq_name = 'Hourly'
        else:
            suggested_freq = 'T'
            freq_name = 'Minutely'

        return {
            'is_regular': is_regular,
            'unique_differences': len(unique_diffs),
            'most_common_diff': str(most_common_diff),
            'most_common_count': most_common_count,
            'total_observations': len(df_temp),
            'suggested_freq': suggested_freq,
            'freq_name': freq_name,
            'diff_distribution': {str(diff): count for diff, count in diff_counts.head(5).items()},
            'regularity_percentage': (most_common_count / len(date_diffs)) * 100
        }

    except Exception as e:
        return {'error': str(e)}

def suggest_frequency_fix(freq_analysis: Dict[str, Any]) -> str:
    """
    Suggest how to fix irregular frequency issues.

    Args:
        freq_analysis: Result from analyze_time_series_frequency

    Returns:
        String with suggestions
    """
    if 'error' in freq_analysis:
        return "Cannot analyze frequency due to data issues."

    if freq_analysis['is_regular']:
        return f"✅ Your data has regular {freq_analysis['freq_name'].lower()} frequency. No fixes needed!"

    regularity = freq_analysis['regularity_percentage']

    if regularity >= 80:
        return f"""
        ⚠️ Your data is mostly regular ({regularity:.1f}% {freq_analysis['freq_name'].lower()}) but has some gaps.

        **Recommendation:** Use fill_missing_dates=True with freq='{freq_analysis['suggested_freq']}'
        This will interpolate missing values and create a regular time series.
        """
    elif regularity >= 50:
        return f"""
        ⚠️ Your data has moderate irregularity ({regularity:.1f}% {freq_analysis['freq_name'].lower()}).

        **Options:**
        1. Resample to {freq_analysis['freq_name'].lower()} frequency (recommended)
        2. Use fill_missing_dates=True with freq='{freq_analysis['suggested_freq']}'
        3. Consider if the irregular pattern is meaningful for your analysis
        """
    else:
        return f"""
        ❌ Your data is highly irregular ({regularity:.1f}% {freq_analysis['freq_name'].lower()}).

        **Recommendations:**
        1. Check if your date column has errors or missing values
        2. Consider resampling to a regular frequency
        3. Review if this data is suitable for time series forecasting
        4. You might need to aggregate or clean your data first
        """
