import streamlit as st

# IMPORTANT: set_page_config MUST be the first Streamlit command
st.set_page_config(
    layout='wide',
    page_title="Time Series Analysis - Forecast with Darts",
    page_icon="📈"
)

# Now import everything else
import pandas as pd
import numpy as np
import logging
import traceback
import time
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# Import app core modules
from app_core.config import (
    init_session_state, get_active_dataset, get_dataset_sample
)
from app_core.utils.styling import apply_css
from app_core.utils.time_series import (
    detect_datetime_columns, detect_numeric_columns, validate_time_series_data,
    create_time_series_plot, prepare_darts_timeseries, get_time_series_info,
    suggest_forecast_horizon
)

# Set up logging
logger = logging.getLogger(__name__)

# Initialize session state
init_session_state()

# Apply custom CSS
apply_css()

# Check for Darts availability
try:
    import darts
    from darts import TimeSeries
    from darts.models import (
        AutoARIMA, Prophet, LinearRegressionModel,
        RandomForest, LightGBMModel, NaiveSeasonal,
        ExponentialSmoothing, Theta
    )
    from darts.metrics import mape, mae, rmse
    from darts.utils.statistics import check_seasonality, plot_acf, plot_pacf
    DARTS_AVAILABLE = True
except ImportError:
    DARTS_AVAILABLE = False

def render_sidebar():
    """Render the sidebar with dataset selection and settings."""
    with st.sidebar:
        st.header("⚙️ Time Series Settings")

        # Dataset selector if multiple datasets exist
        if len(st.session_state.lazy_datasets) > 0:
            st.subheader("Select Dataset")

            dataset_options = list(st.session_state.lazy_datasets.keys())
            selected_dataset = st.selectbox(
                "Choose a dataset:",
                dataset_options,
                index=dataset_options.index(st.session_state.active_dataset) if st.session_state.active_dataset in dataset_options else 0
            )

            # Update the active dataset if changed
            if selected_dataset != st.session_state.active_dataset:
                st.session_state.active_dataset = selected_dataset
                # Clear any cached analysis
                if 'ts_analysis_cache' in st.session_state:
                    del st.session_state.ts_analysis_cache
                st.rerun()

            # Show dataset info
            if st.session_state.active_dataset:
                lazy_dataset = st.session_state.lazy_datasets[st.session_state.active_dataset]
                st.info(f"""
                **Dataset Info:**
                - Rows: {lazy_dataset.shape[0]:,}
                - Columns: {lazy_dataset.shape[1]}
                - Memory: {lazy_dataset.memory_usage_mb:.1f} MB
                """)

def render_empty_state():
    """Render the empty state when no datasets are available."""
    # Create centered layout with columns
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        # Main heading with icon
        st.markdown("""
        <div style="text-align: center;">
            <h1 style="color: #1f77b4;">
                📈 Time Series Analysis
            </h1>
            <p style="font-size: 1.2rem; color: #666; margin-bottom: 2rem;">
                Leverage the power of Darts framework for advanced time series forecasting
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Info box about what's available
        st.info("""
        🔍 **What you can do with Time Series Analysis:**

        • **Data Exploration**: Analyze trends, seasonality, and patterns
        • **Multiple Models**: ARIMA, Prophet, Neural Networks, and more
        • **Forecasting**: Generate accurate predictions with confidence intervals
        • **Model Comparison**: Compare different forecasting approaches
        • **Interactive Visualizations**: Explore your time series data dynamically
        """)

        # Getting started section
        st.markdown("""
        ### 🚀 Get Started

        To start analyzing your time series data, you'll need to upload at least one dataset first.
        Your dataset should contain:
        - A datetime column (dates/timestamps)
        - One or more numeric columns (values to forecast)
        """)

        # Action buttons
        st.markdown("<br>", unsafe_allow_html=True)

        # Create two columns for buttons
        btn_col1, btn_col2 = st.columns(2)

        with btn_col1:
            if st.button("📤 Upload Your Data", type="primary", use_container_width=True):
                st.switch_page("pages/1_Upload_Data.py")

        with btn_col2:
            if st.button("📥 Import from Oracle", type="primary", use_container_width=True):
                st.switch_page("pages/3_Database_Connections.py")

def render_data_preparation_tab():
    """Render the data preparation tab."""
    st.header("📊 Data Preparation")

    if not DARTS_AVAILABLE:
        st.error("""
        ❌ **Darts library not available**

        Please install the Darts library to use time series analysis features:
        ```bash
        pip install darts[all]
        ```
        """)
        return

    # Get the active dataset
    lazy_dataset = get_active_dataset()
    if not lazy_dataset:
        st.warning("No active dataset selected.")
        return

    # Load a sample for column detection
    sample_df = lazy_dataset.get_sample_cached(100)

    st.subheader("🔍 Column Detection")

    # Detect datetime and numeric columns
    datetime_cols = detect_datetime_columns(sample_df)
    numeric_cols = detect_numeric_columns(sample_df)

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Detected Date/Time Columns:**")
        if datetime_cols:
            for col in datetime_cols:
                st.success(f"✅ {col}")
        else:
            st.warning("No datetime columns detected")

    with col2:
        st.markdown("**Detected Numeric Columns:**")
        if numeric_cols:
            for col in numeric_cols[:10]:  # Show first 10
                st.success(f"✅ {col}")
            if len(numeric_cols) > 10:
                st.info(f"... and {len(numeric_cols) - 10} more")
        else:
            st.warning("No numeric columns detected")

    if not datetime_cols or not numeric_cols:
        st.error("❌ Time series analysis requires at least one datetime column and one numeric column.")
        return

    st.subheader("⚙️ Configure Time Series")

    # Column selection
    col1, col2 = st.columns(2)

    with col1:
        selected_date_col = st.selectbox(
            "Select Date/Time Column:",
            datetime_cols,
            help="Choose the column that contains your time information"
        )

    with col2:
        selected_value_cols = st.multiselect(
            "Select Value Columns:",
            numeric_cols,
            default=numeric_cols[:3] if len(numeric_cols) >= 3 else numeric_cols,
            help="Choose the numeric columns you want to analyze and forecast"
        )

    if not selected_value_cols:
        st.warning("Please select at least one value column.")
        return

    # Load full dataset for analysis
    if st.button("🔄 Load Full Dataset for Analysis", type="primary"):
        with st.spinner("Loading full dataset..."):
            try:
                full_df = lazy_dataset.get_full_data()

                # Validate the data
                is_valid, message, cleaned_df = validate_time_series_data(
                    full_df, selected_date_col, selected_value_cols
                )

                if is_valid:
                    st.success(f"✅ {message}")

                    # Store in session state for other tabs
                    st.session_state.ts_data = cleaned_df
                    st.session_state.ts_date_col = selected_date_col
                    st.session_state.ts_value_cols = selected_value_cols

                    # Get time series info
                    ts_info = get_time_series_info(cleaned_df, selected_date_col, selected_value_cols)

                    # Display data info
                    st.subheader("📋 Time Series Information")

                    info_col1, info_col2, info_col3 = st.columns(3)

                    with info_col1:
                        st.metric("Total Observations", f"{ts_info['total_observations']:,}")

                    with info_col2:
                        st.metric("Date Range (Days)", f"{ts_info['date_range']['span_days']:,}")

                    with info_col3:
                        st.metric("Variables", len(selected_value_cols))

                    # Show date range
                    st.markdown(f"""
                    **Date Range:** {ts_info['date_range']['start'].strftime('%Y-%m-%d')} to {ts_info['date_range']['end'].strftime('%Y-%m-%d')}
                    """)

                    # Show data preview
                    st.subheader("👀 Data Preview")
                    preview_cols = [selected_date_col] + selected_value_cols
                    st.dataframe(cleaned_df[preview_cols].head(10), use_container_width=True)

                    # Create initial time series plot
                    st.subheader("📈 Time Series Visualization")
                    fig = create_time_series_plot(
                        cleaned_df, selected_date_col, selected_value_cols,
                        f"Time Series - {st.session_state.active_dataset}"
                    )
                    st.plotly_chart(fig, use_container_width=True)

                else:
                    st.error(f"❌ {message}")

            except Exception as e:
                st.error(f"Error loading dataset: {str(e)}")
                logger.error(f"Error in data preparation: {traceback.format_exc()}")

def render_analysis_tab():
    """Render the time series analysis tab."""
    st.header("🔍 Time Series Analysis")

    # Check if data is prepared
    if 'ts_data' not in st.session_state:
        st.warning("Please prepare your data first in the 'Data Preparation' tab.")
        return

    df = st.session_state.ts_data
    date_col = st.session_state.ts_date_col
    value_cols = st.session_state.ts_value_cols

    # Select which variable to analyze
    selected_var = st.selectbox(
        "Select Variable for Analysis:",
        value_cols,
        help="Choose which time series variable to analyze in detail"
    )

    # Create Darts TimeSeries
    ts = prepare_darts_timeseries(df, date_col, selected_var)
    if ts is None:
        return

    # Analysis options
    st.subheader("📊 Analysis Options")

    analysis_col1, analysis_col2 = st.columns(2)

    with analysis_col1:
        show_decomposition = st.checkbox("Show Decomposition", value=True)
        show_seasonality = st.checkbox("Show Seasonality Analysis", value=True)

    with analysis_col2:
        show_stationarity = st.checkbox("Show Stationarity Tests", value=False)
        show_correlation = st.checkbox("Show Autocorrelation", value=False)

    # Decomposition Analysis
    if show_decomposition:
        st.subheader("📈 Time Series Decomposition")

        try:
            from darts.utils.statistics import extract_trend_and_seasonality

            # Extract components
            trend, seasonal = extract_trend_and_seasonality(ts)
            residual = ts - trend - seasonal

            # Create decomposition plot
            fig = make_subplots(
                rows=4, cols=1,
                subplot_titles=['Original', 'Trend', 'Seasonal', 'Residual'],
                vertical_spacing=0.08
            )

            # Original series
            fig.add_trace(
                go.Scatter(x=ts.time_index, y=ts.values().flatten(),
                          name='Original', line=dict(color='blue')),
                row=1, col=1
            )

            # Trend
            fig.add_trace(
                go.Scatter(x=trend.time_index, y=trend.values().flatten(),
                          name='Trend', line=dict(color='red')),
                row=2, col=1
            )

            # Seasonal
            fig.add_trace(
                go.Scatter(x=seasonal.time_index, y=seasonal.values().flatten(),
                          name='Seasonal', line=dict(color='green')),
                row=3, col=1
            )

            # Residual
            fig.add_trace(
                go.Scatter(x=residual.time_index, y=residual.values().flatten(),
                          name='Residual', line=dict(color='orange')),
                row=4, col=1
            )

            fig.update_layout(height=800, showlegend=False, title_text="Time Series Decomposition")
            st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error in decomposition analysis: {str(e)}")

    # Seasonality Analysis
    if show_seasonality:
        st.subheader("🔄 Seasonality Analysis")

        try:
            # Check for different seasonal periods
            seasonal_periods = [7, 30, 365] if len(ts) > 365 else [7, 30] if len(ts) > 30 else [7] if len(ts) > 14 else []

            if seasonal_periods:
                seasonality_results = {}
                for period in seasonal_periods:
                    if len(ts) > 2 * period:
                        is_seasonal = check_seasonality(ts, m=period)
                        seasonality_results[period] = is_seasonal

                # Display results
                season_col1, season_col2, season_col3 = st.columns(3)

                with season_col1:
                    if 7 in seasonality_results:
                        st.metric("Weekly Seasonality", "✅ Yes" if seasonality_results[7] else "❌ No")

                with season_col2:
                    if 30 in seasonality_results:
                        st.metric("Monthly Seasonality", "✅ Yes" if seasonality_results[30] else "❌ No")

                with season_col3:
                    if 365 in seasonality_results:
                        st.metric("Yearly Seasonality", "✅ Yes" if seasonality_results[365] else "❌ No")
            else:
                st.info("Insufficient data for seasonality analysis (need more data points)")

        except Exception as e:
            st.error(f"Error in seasonality analysis: {str(e)}")

    # Stationarity Tests
    if show_stationarity:
        st.subheader("📊 Stationarity Analysis")

        try:
            from darts.utils.statistics import stationarity_tests

            # Perform stationarity tests
            adf_stat, adf_pvalue = stationarity_tests(ts, test='adf')
            kpss_stat, kpss_pvalue = stationarity_tests(ts, test='kpss')

            stat_col1, stat_col2 = st.columns(2)

            with stat_col1:
                st.markdown("**Augmented Dickey-Fuller Test:**")
                st.metric("ADF Statistic", f"{adf_stat:.4f}")
                st.metric("P-value", f"{adf_pvalue:.4f}")
                if adf_pvalue < 0.05:
                    st.success("✅ Series is stationary (ADF)")
                else:
                    st.warning("⚠️ Series may not be stationary (ADF)")

            with stat_col2:
                st.markdown("**KPSS Test:**")
                st.metric("KPSS Statistic", f"{kpss_stat:.4f}")
                st.metric("P-value", f"{kpss_pvalue:.4f}")
                if kpss_pvalue > 0.05:
                    st.success("✅ Series is stationary (KPSS)")
                else:
                    st.warning("⚠️ Series may not be stationary (KPSS)")

        except Exception as e:
            st.error(f"Error in stationarity analysis: {str(e)}")

    # Autocorrelation Analysis
    if show_correlation:
        st.subheader("🔗 Autocorrelation Analysis")

        try:
            # Create ACF and PACF plots
            fig = make_subplots(
                rows=1, cols=2,
                subplot_titles=['Autocorrelation Function (ACF)', 'Partial Autocorrelation Function (PACF)']
            )

            # Calculate ACF and PACF
            from statsmodels.tsa.stattools import acf, pacf

            ts_values = ts.values().flatten()
            lags = min(40, len(ts_values) // 4)

            acf_values = acf(ts_values, nlags=lags)
            pacf_values = pacf(ts_values, nlags=lags)

            # ACF plot
            fig.add_trace(
                go.Bar(x=list(range(len(acf_values))), y=acf_values, name='ACF'),
                row=1, col=1
            )

            # PACF plot
            fig.add_trace(
                go.Bar(x=list(range(len(pacf_values))), y=pacf_values, name='PACF'),
                row=1, col=2
            )

            fig.update_layout(height=400, showlegend=False)
            st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error in autocorrelation analysis: {str(e)}")

def render_forecasting_tab():
    """Render the forecasting tab."""
    st.header("🔮 Forecasting")

    # Check if data is prepared
    if 'ts_data' not in st.session_state:
        st.warning("Please prepare your data first in the 'Data Preparation' tab.")
        return

    df = st.session_state.ts_data
    date_col = st.session_state.ts_date_col
    value_cols = st.session_state.ts_value_cols

    # Select variable for forecasting
    selected_var = st.selectbox(
        "Select Variable for Forecasting:",
        value_cols,
        help="Choose which time series variable to forecast"
    )

    # Create Darts TimeSeries
    ts = prepare_darts_timeseries(df, date_col, selected_var)
    if ts is None:
        return

    st.subheader("⚙️ Forecasting Configuration")

    # Forecasting parameters
    config_col1, config_col2 = st.columns(2)

    with config_col1:
        # Forecast horizon
        suggested_horizon = suggest_forecast_horizon(df, date_col)
        forecast_horizon = st.number_input(
            "Forecast Horizon (periods):",
            min_value=1,
            max_value=min(100, len(ts) // 2),
            value=suggested_horizon,
            help="Number of future periods to forecast"
        )

        # Train/test split
        test_size = st.slider(
            "Test Set Size (%):",
            min_value=10,
            max_value=40,
            value=20,
            help="Percentage of data to use for testing model performance"
        )

    with config_col2:
        # Model selection
        available_models = {
            "Auto ARIMA": "auto_arima",
            "Prophet": "prophet",
            "Exponential Smoothing": "exp_smoothing",
            "Naive Seasonal": "naive_seasonal",
            "Linear Regression": "linear_regression",
            "Random Forest": "random_forest",
            "Theta": "theta"
        }

        selected_models = st.multiselect(
            "Select Models to Compare:",
            list(available_models.keys()),
            default=["Auto ARIMA", "Prophet", "Exponential Smoothing"],
            help="Choose which forecasting models to train and compare"
        )

        # Confidence intervals
        confidence_level = st.slider(
            "Confidence Level (%):",
            min_value=80,
            max_value=99,
            value=95,
            help="Confidence level for prediction intervals"
        )

    if not selected_models:
        st.warning("Please select at least one forecasting model.")
        return

    # Split data
    split_point = int(len(ts) * (1 - test_size / 100))
    train_ts = ts[:split_point]
    test_ts = ts[split_point:]

    st.info(f"Training on {len(train_ts)} observations, testing on {len(test_ts)} observations")

    # Run forecasting
    if st.button("🚀 Run Forecasting", type="primary"):
        with st.spinner("Training models and generating forecasts..."):
            try:
                results = {}

                for model_name in selected_models:
                    model_key = available_models[model_name]

                    try:
                        # Initialize model
                        if model_key == "auto_arima":
                            model = AutoARIMA()
                        elif model_key == "prophet":
                            model = Prophet()
                        elif model_key == "exp_smoothing":
                            model = ExponentialSmoothing()
                        elif model_key == "naive_seasonal":
                            # Determine seasonal period
                            seasonal_period = 7 if len(train_ts) > 14 else 1
                            model = NaiveSeasonal(K=seasonal_period)
                        elif model_key == "linear_regression":
                            model = LinearRegressionModel(lags=min(10, len(train_ts) // 4))
                        elif model_key == "random_forest":
                            model = RandomForest(lags=min(10, len(train_ts) // 4), n_estimators=100)
                        elif model_key == "theta":
                            model = Theta()

                        # Train model
                        model.fit(train_ts)

                        # Generate forecasts
                        forecast = model.predict(len(test_ts))
                        future_forecast = model.predict(forecast_horizon)

                        # Calculate metrics on test set
                        mae_score = mae(test_ts, forecast)
                        rmse_score = rmse(test_ts, forecast)
                        mape_score = mape(test_ts, forecast)

                        results[model_name] = {
                            'model': model,
                            'forecast': forecast,
                            'future_forecast': future_forecast,
                            'mae': mae_score,
                            'rmse': rmse_score,
                            'mape': mape_score
                        }

                        st.success(f"✅ {model_name} trained successfully")

                    except Exception as e:
                        st.error(f"❌ Error training {model_name}: {str(e)}")
                        continue

                if results:
                    # Store results in session state
                    st.session_state.forecast_results = results
                    st.session_state.train_ts = train_ts
                    st.session_state.test_ts = test_ts
                    st.session_state.forecast_horizon = forecast_horizon

                    # Display results
                    st.subheader("📊 Model Performance Comparison")

                    # Create performance comparison table
                    performance_data = []
                    for model_name, result in results.items():
                        performance_data.append({
                            'Model': model_name,
                            'MAE': f"{result['mae']:.4f}",
                            'RMSE': f"{result['rmse']:.4f}",
                            'MAPE (%)': f"{result['mape']:.2f}"
                        })

                    performance_df = pd.DataFrame(performance_data)
                    st.dataframe(performance_df, use_container_width=True)

                    # Find best model
                    best_model = min(results.keys(), key=lambda x: results[x]['mape'])
                    st.success(f"🏆 Best performing model: **{best_model}** (lowest MAPE)")

                    # Visualization
                    st.subheader("📈 Forecast Visualization")

                    fig = go.Figure()

                    # Plot historical data
                    fig.add_trace(go.Scatter(
                        x=train_ts.time_index,
                        y=train_ts.values().flatten(),
                        mode='lines',
                        name='Training Data',
                        line=dict(color='blue')
                    ))

                    fig.add_trace(go.Scatter(
                        x=test_ts.time_index,
                        y=test_ts.values().flatten(),
                        mode='lines',
                        name='Actual (Test)',
                        line=dict(color='green')
                    ))

                    # Plot forecasts
                    colors = px.colors.qualitative.Set1
                    for i, (model_name, result) in enumerate(results.items()):
                        # Test period forecast
                        fig.add_trace(go.Scatter(
                            x=result['forecast'].time_index,
                            y=result['forecast'].values().flatten(),
                            mode='lines',
                            name=f'{model_name} (Test)',
                            line=dict(color=colors[i % len(colors)], dash='dash')
                        ))

                        # Future forecast
                        fig.add_trace(go.Scatter(
                            x=result['future_forecast'].time_index,
                            y=result['future_forecast'].values().flatten(),
                            mode='lines',
                            name=f'{model_name} (Future)',
                            line=dict(color=colors[i % len(colors)], width=3)
                        ))

                    fig.update_layout(
                        title=f"Time Series Forecasting Results - {selected_var}",
                        xaxis_title="Date",
                        yaxis_title="Value",
                        hovermode='x unified',
                        height=600
                    )

                    st.plotly_chart(fig, use_container_width=True)

                else:
                    st.error("No models were successfully trained.")

            except Exception as e:
                st.error(f"Error in forecasting: {str(e)}")
                logger.error(f"Error in forecasting: {traceback.format_exc()}")

def render_results_tab():
    """Render the results and export tab."""
    st.header("📋 Results & Export")

    # Check if forecasting results exist
    if 'forecast_results' not in st.session_state:
        st.warning("No forecasting results available. Please run forecasting first.")
        return

    results = st.session_state.forecast_results

    st.subheader("📊 Detailed Results")

    # Model selection for detailed view
    selected_model = st.selectbox(
        "Select Model for Detailed View:",
        list(results.keys()),
        help="Choose a model to view detailed results"
    )

    if selected_model:
        result = results[selected_model]

        # Display metrics
        metric_col1, metric_col2, metric_col3 = st.columns(3)

        with metric_col1:
            st.metric("Mean Absolute Error", f"{result['mae']:.4f}")

        with metric_col2:
            st.metric("Root Mean Square Error", f"{result['rmse']:.4f}")

        with metric_col3:
            st.metric("Mean Absolute Percentage Error", f"{result['mape']:.2f}%")

        # Export options
        st.subheader("💾 Export Options")

        export_col1, export_col2 = st.columns(2)

        with export_col1:
            # Export forecast data
            if st.button("📥 Download Future Forecast", use_container_width=True):
                forecast_df = result['future_forecast'].pd_dataframe()
                forecast_df.columns = [f'{selected_model}_forecast']

                csv = forecast_df.to_csv(index=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                st.download_button(
                    label="Download CSV",
                    data=csv,
                    file_name=f"forecast_{selected_model}_{timestamp}.csv",
                    mime="text/csv"
                )

        with export_col2:
            # Export all results
            if st.button("📊 Download All Results", use_container_width=True):
                # Combine all forecasts
                all_forecasts = pd.DataFrame()

                for model_name, model_result in results.items():
                    forecast_df = model_result['future_forecast'].pd_dataframe()
                    forecast_df.columns = [f'{model_name}_forecast']

                    if all_forecasts.empty:
                        all_forecasts = forecast_df
                    else:
                        all_forecasts = all_forecasts.join(forecast_df)

                csv = all_forecasts.to_csv(index=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                st.download_button(
                    label="Download All Forecasts CSV",
                    data=csv,
                    file_name=f"all_forecasts_{timestamp}.csv",
                    mime="text/csv"
                )

# Main application
def main():
    """Main function to render the Time Series Analysis page."""
    # Render sidebar
    render_sidebar()

    # Main page header
    st.title("📈 Time Series Analysis")
    st.markdown("Leverage the power of Darts framework for advanced time series forecasting and analysis.")

    # Check if we have data
    if not st.session_state.has_data or not st.session_state.lazy_datasets:
        render_empty_state()
        return

    # Create tabs for different sections
    tab1, tab2, tab3, tab4 = st.tabs([
        "📊 Data Preparation",
        "🔍 Analysis",
        "🔮 Forecasting",
        "📋 Results & Export"
    ])

    with tab1:
        render_data_preparation_tab()

    with tab2:
        render_analysis_tab()

    with tab3:
        render_forecasting_tab()

    with tab4:
        render_results_tab()

# Run the main function
main()